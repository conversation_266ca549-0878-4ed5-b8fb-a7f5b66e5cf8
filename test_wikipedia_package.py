#!/usr/bin/env python3
"""
Test script for the updated EnhancedWikipediaSearchNode using wikipedia package
"""

import asyncio
import logging
from enhanced_workflow import EnhancedWikipediaSearchNode

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_wikipedia_package():
    """Test the updated Wikipedia search using wikipedia package"""
    print("=== Wikipedia Package Test ===")
    
    wiki_node = EnhancedWikipediaSearchNode()
    
    test_cases = [
        {
            "name": "Original workflow inputs",
            "inputs": {"interest": "cybersecurity companies", "location": "Tokyo"}
        },
        {
            "name": "Simple location",
            "inputs": {"interest": "technology", "location": "Japan"}
        },
        {
            "name": "Well-known topic",
            "inputs": {"interest": "artificial intelligence", "location": "California"}
        },
        {
            "name": "Specific company",
            "inputs": {"interest": "Google", "location": "Mountain View"}
        },
        {
            "name": "Historical topic",
            "inputs": {"interest": "samurai", "location": "Japan"}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"   Inputs: {test_case['inputs']}")
        
        try:
            result = await wiki_node.execute(test_case['inputs'])
            
            if result.get("text"):
                print(f"   ✓ Success! Title: {result['title']}")
                print(f"   ✓ Text length: {len(result['text'])} characters")
                print(f"   ✓ URL: {result['url']}")
                print(f"   ✓ Preview: {result['text'][:150]}...")
            else:
                print(f"   ❌ No results found")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()

async def test_direct_wikipedia_usage():
    """Test the wikipedia package directly"""
    print("\n=== Direct Wikipedia Package Test ===")
    
    try:
        import wikipedia
        
        # Test basic search
        print("1. Testing basic search...")
        search_results = wikipedia.search("Tokyo", results=3)
        print(f"   Search results: {search_results}")
        
        # Test getting a page
        print("\n2. Testing page retrieval...")
        page = wikipedia.page("Tokyo")
        print(f"   Title: {page.title}")
        print(f"   URL: {page.url}")
        print(f"   Summary length: {len(page.summary)} characters")
        print(f"   Summary preview: {page.summary[:200]}...")
        
        # Test disambiguation handling
        print("\n3. Testing disambiguation handling...")
        try:
            mercury_page = wikipedia.page("Mercury")
            print(f"   Got page: {mercury_page.title}")
        except wikipedia.exceptions.DisambiguationError as e:
            print(f"   Disambiguation options: {e.options[:5]}")
            # Try first option
            first_option = wikipedia.page(e.options[0])
            print(f"   First option: {first_option.title}")
        
        # Test language setting
        print("\n4. Testing language setting...")
        wikipedia.set_lang("ja")
        jp_search = wikipedia.search("東京", results=2)
        print(f"   Japanese search results: {jp_search}")
        
        # Reset to English
        wikipedia.set_lang("en")
        
    except ImportError:
        print("   ❌ Wikipedia package not available")
    except Exception as e:
        print(f"   ❌ Error: {e}")

async def main():
    """Run all tests"""
    await test_direct_wikipedia_usage()
    await test_wikipedia_package()
    
    print("\n=== Summary ===")
    print("The EnhancedWikipediaSearchNode has been updated to use the wikipedia package:")
    print("✓ Simpler and more reliable than direct API calls")
    print("✓ Built-in disambiguation handling")
    print("✓ Automatic search fallback")
    print("✓ Language support")
    print("✓ Better error handling")

if __name__ == "__main__":
    asyncio.run(main())
