#!/usr/bin/env python3
"""
Example script demonstrating Serper API usage
Based on the API example from serper.dev
"""

import requests
import json
import os
import asyncio
import aiohttp

def serper_api_sync_example():
    """
    Synchronous example using requests library
    This is the basic example from serper.dev documentation
    """
    print("=== Synchronous Serper API Example ===")
    
    # Get API key from environment
    api_key = os.getenv("SERPER_API_KEY")
    if not api_key:
        print("❌ SERPER_API_KEY environment variable not set")
        print("Please set it with: export SERPER_API_KEY='your_api_key'")
        return
    
    url = "https://google.serper.dev/search"
    
    payload = json.dumps({
        "q": "apple inc"
    })
    
    headers = {
        'X-API-KEY': api_key,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        
        if response.status_code == 200:
            data = response.json()
            print("✓ Request successful!")
            print(f"Found {len(data.get('organic', []))} organic results")
            
            # Display first result
            organic_results = data.get('organic', [])
            if organic_results:
                first_result = organic_results[0]
                print(f"\nFirst result:")
                print(f"Title: {first_result.get('title')}")
                print(f"Link: {first_result.get('link')}")
                print(f"Snippet: {first_result.get('snippet', '')[:100]}...")
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

async def serper_api_async_example():
    """
    Asynchronous example using aiohttp
    This demonstrates how it's used in the workflow
    """
    print("\n=== Asynchronous Serper API Example ===")
    
    # Get API key from environment
    api_key = os.getenv("SERPER_API_KEY")
    if not api_key:
        print("❌ SERPER_API_KEY environment variable not set")
        return
    
    url = "https://google.serper.dev/search"
    
    payload = {
        "q": "python programming tutorials",
        "num": 5,  # Limit to 5 results
        "gl": "us",  # Country: United States
        "hl": "en"   # Language: English
    }
    
    headers = {
        'X-API-KEY': api_key,
        'Content-Type': 'application/json'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    print("✓ Async request successful!")
                    
                    organic_results = data.get('organic', [])
                    print(f"Found {len(organic_results)} organic results")
                    
                    # Display all results
                    for i, result in enumerate(organic_results, 1):
                        print(f"\n{i}. {result.get('title', 'No title')}")
                        print(f"   URL: {result.get('link', 'No URL')}")
                        print(f"   Snippet: {result.get('snippet', 'No snippet')[:80]}...")
                        
                else:
                    print(f"❌ Async request failed with status {response.status}")
                    error_text = await response.text()
                    print(f"Error: {error_text}")
                    
    except Exception as e:
        print(f"❌ Async error: {e}")

def serper_api_advanced_example():
    """
    Advanced example with additional parameters
    """
    print("\n=== Advanced Serper API Example ===")
    
    api_key = os.getenv("SERPER_API_KEY")
    if not api_key:
        print("❌ SERPER_API_KEY environment variable not set")
        return
    
    url = "https://google.serper.dev/search"
    
    # Advanced search with more parameters
    payload = {
        "q": "cybersecurity companies Tokyo",
        "num": 10,
        "gl": "jp",  # Japan
        "hl": "en",  # English
        "type": "search",  # Search type
        "tbs": "qdr:y"  # Time filter: past year
    }
    
    headers = {
        'X-API-KEY': api_key,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print("✓ Advanced search successful!")
            
            # Extract different types of results
            organic_results = data.get('organic', [])
            knowledge_graph = data.get('knowledgeGraph', {})
            answer_box = data.get('answerBox', {})
            
            print(f"Organic results: {len(organic_results)}")
            print(f"Knowledge graph: {'Yes' if knowledge_graph else 'No'}")
            print(f"Answer box: {'Yes' if answer_box else 'No'}")
            
            # Display knowledge graph if available
            if knowledge_graph:
                print(f"\nKnowledge Graph:")
                print(f"Title: {knowledge_graph.get('title', 'N/A')}")
                print(f"Type: {knowledge_graph.get('type', 'N/A')}")
                print(f"Description: {knowledge_graph.get('description', 'N/A')[:100]}...")
            
            # Display answer box if available
            if answer_box:
                print(f"\nAnswer Box:")
                print(f"Answer: {answer_box.get('answer', 'N/A')[:100]}...")
                print(f"Source: {answer_box.get('link', 'N/A')}")
            
            # Display top 3 organic results
            print(f"\nTop 3 Organic Results:")
            for i, result in enumerate(organic_results[:3], 1):
                print(f"\n{i}. {result.get('title', 'No title')}")
                print(f"   URL: {result.get('link', 'No URL')}")
                print(f"   Snippet: {result.get('snippet', 'No snippet')[:100]}...")
                
        else:
            print(f"❌ Advanced search failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Advanced search error: {e}")

def compare_with_workflow():
    """
    Example showing how the API is used in the actual workflow
    """
    print("\n=== Workflow Integration Example ===")
    
    # This simulates how the workflow uses the API
    api_key = os.getenv("SERPER_API_KEY")
    if not api_key:
        print("❌ SERPER_API_KEY environment variable not set")
        return
    
    # Simulate workflow inputs
    workflow_inputs = {
        "interest": "cybersecurity companies",
        "location": "Tokyo",
        "query": "cybersecurity companies Tokyo"
    }
    
    url = "https://google.serper.dev/search"
    
    payload = {
        "q": workflow_inputs["query"],
        "num": 10,
        "gl": "jp",  # From WorkflowConfig.GOOGLE_COUNTRY
        "hl": "en"   # From WorkflowConfig.GOOGLE_LANGUAGE
    }
    
    headers = {
        'X-API-KEY': api_key,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            data = response.json()
            organic_results = data.get("organic", [])
            
            # Transform to workflow format
            workflow_results = []
            for item in organic_results:
                workflow_results.append({
                    "title": item.get("title", ""),
                    "snippet": item.get("snippet", ""),
                    "link": item.get("link", ""),
                    "displayLink": item.get("displayedLink", "")
                })
            
            print("✓ Workflow simulation successful!")
            print(f"Transformed {len(workflow_results)} results for workflow processing")
            
            # Show how results would be formatted for LLM
            formatted_results = []
            for result in workflow_results[:3]:  # Show first 3
                formatted_results.append(f"Title: {result['title']}")
                formatted_results.append(f"Snippet: {result['snippet']}")
                formatted_results.append(f"Link: {result['link']}")
                formatted_results.append("---")
            
            print("\nFormatted for LLM processing:")
            print("\n".join(formatted_results))
            
        else:
            print(f"❌ Workflow simulation failed with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Workflow simulation error: {e}")

def main():
    """Main function to run all examples"""
    print("Serper API Examples")
    print("=" * 50)
    
    # Run synchronous example
    serper_api_sync_example()
    
    # Run asynchronous example
    asyncio.run(serper_api_async_example())
    
    # Run advanced example
    serper_api_advanced_example()
    
    # Run workflow integration example
    compare_with_workflow()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nTo use in your own code:")
    print("1. Set SERPER_API_KEY environment variable")
    print("2. Use the patterns shown above")
    print("3. Handle errors appropriately")
    print("4. Respect rate limits (1 req/sec for free tier)")

if __name__ == "__main__":
    main()
