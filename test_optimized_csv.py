#!/usr/bin/env python3
"""
Test script for optimized CSV output functionality
"""

import asyncio
import os
from enhanced_workflow import EnhancedTargetExploitWorkflow
from target_exploit_workflow import WorkflowInput

async def test_optimized_csv_output():
    """Test the new optimized CSV output functionality"""
    print("=== Testing Optimized CSV Output ===")
    
    # Create workflow instance
    workflow = EnhancedTargetExploitWorkflow()
    
    # Test with a simple input
    workflow_input = WorkflowInput(
        location="Tokyo",
        interest="cybersecurity companies"
    )
    
    print(f"Testing with location: {workflow_input.location}")
    print(f"Testing with interest: {workflow_input.interest}")
    print()
    
    try:
        # Execute workflow
        print("Executing workflow...")
        results = await workflow.execute(workflow_input)
        
        print(f"Found {len(results)} entities")
        
        # Test the new optimized CSV export
        print("\nExporting to optimized CSV files...")
        entities_csv, subdomains_csv = workflow.export_optimized_results_to_csv(results, workflow_input)
        
        if entities_csv and subdomains_csv:
            print(f"✓ Entities CSV created: {entities_csv}")
            print(f"✓ Subdomains CSV created: {subdomains_csv}")
            
            # Check if files exist and show their content
            if os.path.exists(entities_csv):
                print(f"\n--- Content of {entities_csv} ---")
                with open(entities_csv, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content[:500] + "..." if len(content) > 500 else content)
            
            if os.path.exists(subdomains_csv):
                print(f"\n--- Content of {subdomains_csv} ---")
                with open(subdomains_csv, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print("❌ Failed to create CSV files")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_optimized_csv_output())
