#!/usr/bin/env python3
"""
Debug script for EnhancedWikipediaSearchNode using OpenSearch API
"""

import asyncio
import aiohttp
import logging
from enhanced_workflow import EnhancedWikipediaSearchNode
from config import WorkflowConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_wikipedia_opensearch():
    """Test Wikipedia OpenSearch API directly"""
    print("=== Wikipedia OpenSearch API Test ===")

    # Test inputs - using simpler terms that are more likely to have Wikipedia articles
    test_inputs = {
        "interest": "cybersecurity",
        "location": "Japan"
    }

    query = f"{test_inputs['interest']} {test_inputs['location']}"
    language = WorkflowConfig.SEARCH_CONFIG["wikipedia"]["language"]

    print(f"Search query: '{query}'")
    print(f"Language: {language}")

    async with aiohttp.ClientSession() as session:
        # Test OpenSearch API
        opensearch_url = f"https://{language}.wikipedia.org/w/api.php"
        opensearch_params = {
            "action": "opensearch",
            "search": query,
            "limit": 5,
            "namespace": 0,
            "format": "json"
        }

        print(f"\nTesting OpenSearch API")
        print(f"URL: {opensearch_url}")
        print(f"Params: {opensearch_params}")

        try:
            async with session.get(opensearch_url, params=opensearch_params) as response:
                print(f"Response status: {response.status}")

                if response.status == 200:
                    data = await response.json()
                    print(f"Response structure: {type(data)}")
                    print(f"Response length: {len(data) if isinstance(data, list) else 'Not a list'}")

                    if isinstance(data, list) and len(data) >= 4:
                        query_term = data[0]
                        titles = data[1]
                        descriptions = data[2]
                        urls = data[3]

                        print(f"Query term: {query_term}")
                        print(f"Found {len(titles)} titles")
                        print(f"Found {len(descriptions)} descriptions")
                        print(f"Found {len(urls)} URLs")

                        for i in range(min(3, len(titles))):
                            print(f"\n{i+1}. Title: {titles[i] if i < len(titles) else 'N/A'}")
                            print(f"   Description: {descriptions[i] if i < len(descriptions) else 'N/A'}")
                            print(f"   URL: {urls[i] if i < len(urls) else 'N/A'}")

                        if len(titles) > 0:
                            return {
                                "text": descriptions[0] if len(descriptions) > 0 else "",
                                "title": titles[0],
                                "url": urls[0] if len(urls) > 0 else ""
                            }
                    else:
                        print(f"Unexpected response format: {data}")
                else:
                    print(f"API error: {response.status}")
                    response_text = await response.text()
                    print(f"Error response: {response_text[:500]}")

        except Exception as e:
            print(f"Exception during OpenSearch test: {e}")
            import traceback
            traceback.print_exc()

    return None

async def test_wikipedia_node():
    """Test the EnhancedWikipediaSearchNode"""
    print("\n=== Wikipedia Node Test ===")

    # Create Wikipedia search node
    wiki_node = EnhancedWikipediaSearchNode()

    # Test inputs - using simpler terms
    test_inputs = {
        "interest": "cybersecurity",
        "location": "Japan"
    }

    print(f"Test inputs: {test_inputs}")

    try:
        result = await wiki_node.execute(test_inputs)
        print(f"Node result: {result}")

        if result.get("text"):
            print(f"✓ Success! Got {len(result['text'])} characters of text")
            print(f"Title: {result.get('title', 'No title')}")
            print(f"URL: {result.get('url', 'No URL')}")
            print(f"Text preview: {result['text'][:200]}...")
        else:
            print("❌ No text content returned")

    except Exception as e:
        print(f"❌ Error in node execution: {e}")
        import traceback
        traceback.print_exc()

async def test_simple_search():
    """Test with a simple, well-known term"""
    print("\n=== Simple Search Test (Tokyo) ===")

    async with aiohttp.ClientSession() as session:
        opensearch_url = "https://en.wikipedia.org/w/api.php"
        opensearch_params = {
            "action": "opensearch",
            "search": "Tokyo",
            "limit": 3,
            "namespace": 0,
            "format": "json"
        }

        try:
            async with session.get(opensearch_url, params=opensearch_params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"Tokyo search results: {data}")

                    if isinstance(data, list) and len(data) >= 4 and len(data[1]) > 0:
                        print(f"✓ Found {len(data[1])} results for Tokyo")
                        print(f"First result: {data[1][0]}")
                        if len(data[2]) > 0:
                            print(f"Description: {data[2][0][:100]}...")
                    else:
                        print("❌ No results for Tokyo")
                else:
                    print(f"❌ Error: {response.status}")
        except Exception as e:
            print(f"❌ Exception: {e}")

async def test_wikipedia_search():
    """Run all Wikipedia tests"""
    await test_simple_search()
    await test_wikipedia_opensearch()
    await test_wikipedia_node()

if __name__ == "__main__":
    asyncio.run(test_wikipedia_search())
