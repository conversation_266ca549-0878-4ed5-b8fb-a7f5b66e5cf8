#!/usr/bin/env python3
"""
Test script for Serper API integration (serper.dev)
"""

import os
import asyncio
import json
import requests
from enhanced_workflow import EnhancedSerperSearchNode
from config import WorkflowConfig

async def test_serper_api():
    """Test Serper API functionality"""
    print("=== Serper API Test ===")
    
    # Check if API key is configured
    if not WorkflowConfig.SERPER_API_KEY:
        print("❌ SERPER_API_KEY not configured")
        print("Please set the SERPER_API_KEY environment variable")
        print("Example: export SERPER_API_KEY='your_api_key_here'")
        return
    
    print(f"✓ Serper API Key configured: {WorkflowConfig.SERPER_API_KEY[:10]}...")
    
    # Create search node
    search_node = EnhancedSerperSearchNode("test_serper_search", "Test Serper Search")
    
    # Test inputs
    test_inputs = {
        "interest": "cybersecurity companies",
        "location": "Tokyo",
        "query": "cybersecurity companies Tokyo"
    }
    
    print(f"\n🔍 Testing search query: '{test_inputs['query']}'")
    
    try:
        # Execute search
        result = await search_node.execute(test_inputs, search_num=5)
        
        # Display results
        if "json" in result and result["json"]:
            print(f"✓ Search successful! Found {len(result['json'])} results:")
            print("-" * 80)
            
            for i, item in enumerate(result["json"][:3], 1):  # Show first 3 results
                print(f"\n{i}. {item.get('title', 'No title')}")
                print(f"   URL: {item.get('link', 'No URL')}")
                print(f"   Snippet: {item.get('snippet', 'No snippet')[:100]}...")
                
        else:
            print("❌ No search results returned")
            
    except Exception as e:
        print(f"❌ Search failed: {e}")

def test_serper_api_direct():
    """Test Serper API directly with requests"""
    print("\n=== Direct Serper API Test ===")
    
    if not WorkflowConfig.SERPER_API_KEY:
        print("❌ SERPER_API_KEY not configured")
        return
    
    url = "https://google.serper.dev/search"
    
    payload = {
        "q": "apple inc"
    }
    
    headers = {
        'X-API-KEY': WorkflowConfig.SERPER_API_KEY,
        'Content-Type': 'application/json'
    }
    
    try:
        print("🔍 Testing direct API call...")
        response = requests.post(url, headers=headers, json=payload, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            organic_results = data.get("organic", [])
            
            print(f"✓ Direct API call successful! Found {len(organic_results)} results")
            
            if organic_results:
                print("\nFirst result:")
                first_result = organic_results[0]
                print(f"Title: {first_result.get('title', 'No title')}")
                print(f"Link: {first_result.get('link', 'No link')}")
                print(f"Snippet: {first_result.get('snippet', 'No snippet')[:100]}...")
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Direct API call failed: {e}")

async def test_both_apis():
    """Test both SERP and Serper APIs for comparison"""
    print("\n=== API Comparison Test ===")
    
    # Test Serper API
    if WorkflowConfig.SERPER_API_KEY:
        print("\n🔍 Testing Serper API...")
        serper_node = EnhancedSerperSearchNode("test_serper", "Test Serper")
        serper_result = await serper_node.execute({"query": "python programming"}, search_num=3)
        serper_count = len(serper_result.get("json", []))
        print(f"Serper API: {serper_count} results")
    else:
        print("Serper API: Not configured")
    
    # Test SERP API
    if WorkflowConfig.SERP_API_KEY:
        print("\n🔍 Testing SERP API...")
        from enhanced_workflow import EnhancedSerpSearchNode
        serp_node = EnhancedSerpSearchNode("test_serp", "Test SERP")
        serp_result = await serp_node.execute({"query": "python programming"}, search_num=3)
        serp_count = len(serp_result.get("json", []))
        print(f"SERP API: {serp_count} results")
    else:
        print("SERP API: Not configured")

def main():
    """Main test function"""
    print("Serper API Integration Test")
    print("=" * 50)
    
    # Test direct API call first
    test_serper_api_direct()
    
    # Test async integration
    asyncio.run(test_serper_api())
    
    # Test comparison
    asyncio.run(test_both_apis())
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
