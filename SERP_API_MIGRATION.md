# Migration from Google API to SERP API

This document outlines the changes made to replace Google Custom Search API with SERP API.

## Changes Made

### 1. Configuration Updates (`config.py`)

**Before:**
```python
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")
GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID", "")
```

**After:**
```python
SERP_API_KEY = os.getenv("SERP_API_KEY", "")
```

**Search Configuration:**
- Changed from `"google"` to `"serp"` configuration
- Added `"engine": "google"` parameter for SERP API
- Removed dependency on Google CSE ID

### 2. Enhanced Workflow Updates (`enhanced_workflow.py`)

**Class Rename:**
- `EnhancedGoogleSearchNode` → `EnhancedSerpSearchNode`

**API Integration:**
- Replaced Google Custom Search API calls with SERP API library
- Added fallback handling when SERP API library is not available
- Updated error handling and logging

**Key Changes:**
- Uses `google-search-results` Python package
- Synchronous SERP API calls (library doesn't support async)
- Better error handling with fallback to mock data

### 3. Base Workflow Updates (`target_exploit_workflow.py`)

**Class Rename:**
- `GoogleSearchNode` → `SerpSearchNode`

**Node Setup:**
- Updated node titles from "谷歌搜索" to "SERP搜索"
- Maintained same functionality with mock data

### 4. CLI Updates (`cli.py`)

**Configuration Check:**
- Removed Google API Key and CSE ID checks
- Added SERP API Key check

### 5. Documentation Updates (`README.md`)

**API Setup Section:**
- Replaced Google Custom Search API setup instructions
- Added SERP API setup instructions
- Updated environment variable examples

**Environment Variables:**
```bash
# Old
export GOOGLE_API_KEY="your_google_api_key"
export GOOGLE_CSE_ID="your_custom_search_engine_id"

# New
export SERP_API_KEY="your_serp_api_key"
```

### 6. Dependencies (`requirements.txt`)

**Added:**
```
google-search-results>=2.4.2
```

## Benefits of SERP API

1. **Simpler Setup**: Only requires one API key (no CSE ID needed)
2. **Better Rate Limits**: More generous free tier (100 searches/month)
3. **Consistent Results**: Standardized JSON response format
4. **Multiple Engines**: Can easily switch between Google, Bing, etc.
5. **No Quota Management**: No need to manage Google Cloud quotas

## Migration Steps

1. **Get SERP API Key:**
   - Sign up at [serpapi.com](https://serpapi.com/)
   - Get API key from dashboard

2. **Install Dependencies:**
   ```bash
   pip install google-search-results
   ```

3. **Set Environment Variable:**
   ```bash
   export SERP_API_KEY="your_serp_api_key"
   ```

4. **Test Integration:**
   ```bash
   python test_serp_api.py
   ```

## Backward Compatibility

- Mock data functionality remains unchanged
- Same output format maintained
- All existing workflow logic preserved
- Graceful fallback when SERP API is unavailable

## Testing

Use the provided test script to verify the integration:

```bash
python test_serp_api.py
```

This will test the SERP API connection and display sample results.
