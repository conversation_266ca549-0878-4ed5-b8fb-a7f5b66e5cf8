#!/usr/bin/env python3
"""
Test script for the updated EnhancedWikipediaSearchNode that returns all search results
"""

import asyncio
import logging
from enhanced_workflow import EnhancedWikipediaSearchNode

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_all_results():
    """Test the updated Wikipedia search that returns all results"""
    print("=== Wikipedia All Results Test ===")
    
    wiki_node = EnhancedWikipediaSearchNode()
    
    test_cases = [
        {
            "name": "Cybersecurity companies in Tokyo",
            "inputs": {"interest": "cybersecurity companies", "location": "Tokyo"}
        },
        {
            "name": "Technology in Japan",
            "inputs": {"interest": "technology", "location": "Japan"}
        },
        {
            "name": "Artificial Intelligence",
            "inputs": {"interest": "artificial intelligence", "location": "California"}
        },
        {
            "name": "Disambiguation test (Mercury)",
            "inputs": {"interest": "Mercury", "location": ""}
        },
        {
            "name": "Startups in Silicon Valley",
            "inputs": {"interest": "startups", "location": "Silicon Valley"}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"{i}. Testing: {test_case['name']}")
        print(f"   Inputs: {test_case['inputs']}")
        print('='*60)
        
        try:
            result = await wiki_node.execute(test_case['inputs'])
            
            if result.get("results"):
                print(f"✓ Found {result['total_results']} unique results:")
                
                for j, res in enumerate(result["results"], 1):
                    print(f"\n   Result {j}:")
                    print(f"   Title: {res['title']}")
                    print(f"   Type: {res['type']}")
                    print(f"   Query: {res['query']}")
                    if res.get('original_option'):
                        print(f"   Original Option: {res['original_option']}")
                    if res.get('search_title'):
                        print(f"   Search Title: {res['search_title']}")
                    print(f"   URL: {res['url']}")
                    print(f"   Text Preview: {res['text'][:150]}...")
                
                print(f"\n   Backward Compatibility:")
                print(f"   Primary Title: {result['title']}")
                print(f"   Primary Text Length: {len(result['text'])} characters")
                
            else:
                print(f"   ❌ No results found")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()

async def test_result_types():
    """Test different types of results"""
    print(f"\n{'='*60}")
    print("Testing Different Result Types")
    print('='*60)
    
    wiki_node = EnhancedWikipediaSearchNode()
    
    # Test direct page
    print("\n1. Testing Direct Page (Tokyo):")
    result = await wiki_node.execute({"interest": "Tokyo", "location": ""})
    if result.get("results"):
        for res in result["results"]:
            print(f"   - {res['title']} (type: {res['type']})")
    
    # Test disambiguation
    print("\n2. Testing Disambiguation (Mercury):")
    result = await wiki_node.execute({"interest": "Mercury", "location": ""})
    if result.get("results"):
        for res in result["results"]:
            print(f"   - {res['title']} (type: {res['type']}, option: {res.get('original_option', 'N/A')})")
    
    # Test search results
    print("\n3. Testing Search Results (Python programming):")
    result = await wiki_node.execute({"interest": "Python programming", "location": ""})
    if result.get("results"):
        for res in result["results"]:
            print(f"   - {res['title']} (type: {res['type']}, search: {res.get('search_title', 'N/A')})")

async def demonstrate_usage():
    """Demonstrate how to use the new all-results functionality"""
    print(f"\n{'='*60}")
    print("Usage Demonstration")
    print('='*60)
    
    wiki_node = EnhancedWikipediaSearchNode()
    
    result = await wiki_node.execute({
        "interest": "machine learning", 
        "location": "Stanford"
    })
    
    print(f"Total results found: {result.get('total_results', 0)}")
    print(f"Backward compatibility - Primary result: {result.get('title', 'None')}")
    
    print("\nAll results:")
    for i, res in enumerate(result.get("results", []), 1):
        print(f"{i}. {res['title']} ({res['type']} from query: '{res['query']}')")
    
    print("\nHow to access in workflow:")
    print("# Get all results")
    print("all_results = result['results']")
    print("for res in all_results:")
    print("    print(f'Title: {res[\"title\"]}, Text: {res[\"text\"][:100]}...')")
    print()
    print("# Get primary result (backward compatible)")
    print("primary_text = result['text']")
    print("primary_title = result['title']")

async def main():
    """Run all tests"""
    await test_all_results()
    await test_result_types()
    await demonstrate_usage()
    
    print(f"\n{'='*60}")
    print("Summary of New Features")
    print('='*60)
    print("✓ Returns ALL search results instead of just the first one")
    print("✓ Maintains backward compatibility with existing code")
    print("✓ Provides result metadata (type, query, original_option, etc.)")
    print("✓ Removes duplicate results based on title")
    print("✓ Supports multiple result types: direct, disambiguation, search")
    print("✓ Includes total_results count")
    print("✓ Each result includes full Wikipedia page summary")

if __name__ == "__main__":
    asyncio.run(main())
