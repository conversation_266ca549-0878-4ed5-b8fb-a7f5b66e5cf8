#!/usr/bin/env python3
"""
Test script for SERP API integration
"""

import os
import asyncio
from enhanced_workflow import EnhancedSerpSearchNode
from config import WorkflowConfig

async def test_serp_api():
    """Test SERP API functionality"""
    print("=== SERP API Test ===")
    
    # Check if API key is configured
    if not WorkflowConfig.SERP_API_KEY:
        print("❌ SERP_API_KEY not configured")
        print("Please set the SERP_API_KEY environment variable")
        print("Example: export SERP_API_KEY='your_api_key_here'")
        return
    
    print(f"✓ SERP API Key configured: {WorkflowConfig.SERP_API_KEY[:10]}...")
    
    # Create search node
    search_node = EnhancedSerpSearchNode("test_search", "Test SERP Search")
    
    # Test inputs
    test_inputs = {
        "interest": "cybersecurity companies",
        "location": "Tokyo",
        "query": "cybersecurity companies Tokyo"
    }
    
    print(f"Testing search query: {test_inputs['query']}")
    
    try:
        # Execute search
        result = await search_node.execute(test_inputs)
        
        if result and "json" in result:
            search_results = result["json"]
            print(f"✓ Search successful! Found {len(search_results)} results")
            
            # Display first few results
            for i, item in enumerate(search_results[:3], 1):
                print(f"\n{i}. {item.get('title', 'No title')}")
                print(f"   Link: {item.get('link', 'No link')}")
                print(f"   Snippet: {item.get('snippet', 'No snippet')[:100]}...")
        else:
            print("❌ Search failed or returned no results")
            print(f"Result: {result}")
            
    except Exception as e:
        print(f"❌ Error during search: {e}")

if __name__ == "__main__":
    asyncio.run(test_serp_api())
