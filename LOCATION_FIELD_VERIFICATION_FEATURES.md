# Entity and Subdomain Location/Field Verification Features

This document describes the three new features implemented for the entity and subdomain discovery workflow to enhance relevance verification and entity persistence.

## Overview

The enhanced workflow now includes:

1. **Entity Location and Field Verification**: Validates that discovered entities are relevant to the specified geographic location and industry field
2. **Subdomain Location and Field Verification**: Extends subdomain discovery to validate location and field relevance
3. **Entity Persistence**: Provides functionality to save and load verified entities for reuse across workflow runs

## Feature 1: Entity Location and Field Verification

### Description
This feature adds LLM-based verification to ensure discovered entities are actually relevant to the user's specified location and industry field criteria.

### Implementation Details

#### New LLM Node
- **Node Type**: `entity_location_verification`
- **Purpose**: Analyzes entities against location and field criteria
- **Model Configuration**: Uses the same model as other verification tasks (configurable in `config.py`)

#### Verification Process
1. For each discovered entity, the system performs a targeted search
2. Search results are analyzed by the LLM to determine:
   - **Location Relevance**: Does the entity operate in or serve the target location?
   - **Field Relevance**: Does the entity belong to the specified industry/field?
   - **Entity Type**: Is this a legitimate business/organization?
   - **Current Status**: Is the entity currently active/operational?

#### Classification System
- **RELEVANT**: Entity matches both location and field criteria
- **LOCATION_MISMATCH**: Entity belongs to target field but not in target location
- **FIELD_MISMATCH**: Entity is in target location but not in target field
- **IRRELEVANT**: Entity doesn't match location or field criteria
- **INSUFFICIENT_DATA**: Not enough information to make determination

#### Verification Output
```json
{
  "entity_verification": {
    "entity": "entity_name",
    "location_relevance": "relevant|not_relevant|uncertain",
    "field_relevance": "relevant|not_relevant|uncertain",
    "overall_classification": "RELEVANT|LOCATION_MISMATCH|FIELD_MISMATCH|IRRELEVANT|INSUFFICIENT_DATA",
    "confidence": "high|medium|low",
    "location_evidence": "Brief explanation of location relevance",
    "field_evidence": "Brief explanation of field relevance",
    "recommendation": "include|exclude|needs_review"
  }
}
```

### Usage
The verification is automatically applied during workflow execution. Entities with `recommendation: "exclude"` are filtered out, while those with `needs_review` are flagged but included.

## Feature 2: Subdomain Location and Field Verification

### Description
Extends the existing subdomain verification to analyze whether subdomains serve content relevant to the specified location and industry field.

### Implementation Details

#### New LLM Node
- **Node Type**: `subdomain_location_verification`
- **Purpose**: Analyzes subdomain relevance to location and field criteria
- **Integration**: Works alongside existing organizational subdomain verification

#### Verification Process
1. After organizational subdomain verification, location/field verification is performed
2. Each subdomain is analyzed for:
   - **Geographic Relevance**: Does it serve content specific to the target location?
   - **Field Relevance**: Does it serve content related to the target industry?
   - **Content Purpose**: What is the primary purpose of this subdomain?
   - **Language/Localization**: Does it use relevant language/localization?

#### Classification System
- **BOTH_RELEVANT**: Subdomain is relevant to both location and field
- **LOCATION_RELEVANT**: Subdomain specifically serves the target location
- **FIELD_RELEVANT**: Subdomain serves content related to the target field
- **GENERAL**: General corporate subdomain (organizational but not location/field specific)
- **IRRELEVANT**: Subdomain not relevant to location or field criteria

#### Filtering Logic
Subdomains are filtered based on their classification:
- **Included**: BOTH_RELEVANT, LOCATION_RELEVANT, FIELD_RELEVANT, GENERAL
- **Excluded**: IRRELEVANT

### Usage
The verification is automatically applied after organizational subdomain verification. Results are displayed in the CLI output and included in CSV exports.

## Feature 3: Entity Persistence

### Description
Provides functionality to save verified entities after extraction and validation, and load them in subsequent workflow runs to build cumulative intelligence.

### Implementation Details

#### Persistent Storage
- **Directory**: `persistent_storage/` (created automatically)
- **Format**: Enhanced CSV with verification metadata
- **Naming**: `persistent_entities_{location}_{interest}_{timestamp}.csv`

#### Enhanced CSV Export
The CSV export now includes verification data:
- `location_verification_status`: Entity location relevance
- `location_verification_confidence`: Confidence level
- `field_verification_status`: Entity field relevance
- `overall_classification`: Overall verification result
- `location_evidence`: Evidence for location relevance
- `field_evidence`: Evidence for field relevance
- `recommendation`: Include/exclude/needs_review
- `subdomain_location_verifications`: Subdomain verification details

#### Loading and Merging
- **Load Function**: `load_entities_from_csv(filepath)`
- **Merge Function**: `merge_entities_with_existing(new_entities, existing_entities)`
- **Duplicate Handling**: Entities are deduplicated by name (case-insensitive)

### CLI Usage

#### Save to Persistent Storage
```bash
python cli.py --location "Tokyo" --interest "fintech" --enhanced --save-persistent
```

#### Load and Merge Previous Results
```bash
python cli.py --location "Tokyo" --interest "fintech" --enhanced --load-entities persistent_storage/previous_results.csv
```

#### Combined Usage
```bash
python cli.py --location "Tokyo" --interest "fintech" --enhanced \
  --load-entities persistent_storage/previous_results.csv \
  --export-csv --save-persistent
```

## Configuration

### Model Configuration
Add new model configurations in `config.py`:

```python
MODELS = {
    # ... existing models ...
    "entity_location_verification": {
        "name": "qwen3:32b",
        "provider": "ollama",
        "format": "json"
    },
    "subdomain_location_verification": {
        "name": "qwen3:32b",
        "provider": "ollama",
        "format": "json"
    }
}
```

### Prompt Templates
New prompts are defined in `config.py` under the `PROMPTS` dictionary:
- `entity_location_verification`: For entity verification
- `subdomain_location_verification`: For subdomain verification

## Output Examples

### CLI Output with Verification
```
1. Entity: Example Corp
   Link: https://example.com
   Domain: example.com
   ✅ Verification: RELEVANT (confidence: high)
      📍 Location: Entity operates in Tokyo
      🏢 Field: Entity belongs to fintech industry
   📡 Subdomains (3):
      • api.example.com [FIELD_RELEVANT, high]
        Purpose: API services for financial products
      • tokyo.example.com [BOTH_RELEVANT, high]
        Purpose: Regional operations in Tokyo
      • support.example.com [GENERAL, medium]
        Purpose: Customer support portal
```

### Enhanced CSV Export
The CSV now includes columns for all verification metadata, enabling detailed analysis and filtering of results.

## Integration with Existing Features

### Compatibility
- Fully compatible with existing SERP API integration
- Works with existing LLM verification for organizational entities
- Maintains backward compatibility with existing CSV export functionality
- Integrates seamlessly with existing CLI options

### Performance Considerations
- Additional LLM calls for verification may increase processing time
- Verification can be disabled by modifying the workflow if needed
- Mock responses are provided for testing without API access

## Benefits

1. **Improved Accuracy**: Filters out irrelevant entities and subdomains
2. **Location Targeting**: Ensures results are geographically relevant
3. **Industry Focus**: Maintains focus on the specified business domain
4. **Cumulative Intelligence**: Builds knowledge base over multiple runs
5. **Detailed Metadata**: Provides rich verification information for analysis
6. **Flexible Usage**: Supports both one-time and cumulative research workflows
