# Target Exploit Workflow - Python Implementation

This project converts a Dify DSL (Domain Specific Language) workflow into a Python implementation for OSINT (Open Source Intelligence) research. The workflow performs automated target exploitation research by gathering information from multiple sources including Wikipedia, Google Search, and LLM analysis.

## Features

- **Multi-source Information Gathering**: Integrates Wikipedia search, Google Custom Search, and LLM knowledge extraction
- **Entity Extraction**: Automatically identifies and extracts relevant entities from search results
- **Domain Discovery**: Finds official websites and domains for discovered entities
- **Iterative Processing**: Processes each discovered entity individually for detailed analysis
- **Flexible API Support**: Supports multiple LLM providers (Ollama, OpenAI) and search APIs
- **Mock Mode**: Can run with mock data when APIs are not configured

## Project Structure

```
dify2python/
├── target_exploit_workflow.py    # Basic workflow implementation with mock data
├── enhanced_workflow.py          # Enhanced version with real API integrations
├── config.py                     # Configuration and settings
├── cli.py                        # Command-line interface
├── requirements.txt              # Python dependencies
├── README.md                     # This file
└── target-exploit.yml           # Original Dify DSL file
```

## Installation

1. Clone or download this repository
2. Install Python dependencies:

```bash
pip install -r requirements.txt
```

## Configuration

### Environment Variables

Set the following environment variables for full functionality:

```bash
# Search APIs (At least one required for enhanced mode)
export SERP_API_KEY="your_serp_api_key"        # SerpApi (serpapi.com)
export SERPER_API_KEY="your_serper_api_key"    # Serper API (serper.dev)

# OpenAI API (Optional - for OpenAI LLM provider)
export OPENAI_API_KEY="your_openai_api_key"

# Ollama Configuration (Optional - for local LLM)
export OLLAMA_BASE_URL="http://localhost:11434"

# Search Configuration
export google_country="jp"  # Country code for search
export google_lan="en"      # Language code for search
```

### API Setup

#### Search APIs (Choose One or Both)

**Option 1: Serper API (Recommended)**
1. Go to [Serper.dev](https://serper.dev/)
2. Sign up for a free account (2,500 searches/month)
3. Get your API key from the dashboard
4. Set the `SERPER_API_KEY` environment variable

**Option 2: SERP API**
1. Go to [SerpApi](https://serpapi.com/)
2. Sign up for a free account (100 searches/month)
3. Get your API key from the dashboard
4. Set the `SERP_API_KEY` environment variable

**Note:** The system will automatically prefer Serper API if both are configured, as it offers more generous free tier limits.

#### Ollama (Local LLM)
1. Install [Ollama](https://ollama.ai/)
2. Pull required models:
```bash
ollama pull qwen3:32b
ollama pull qwq:latest
```

## Usage

### Command Line Interface

Check configuration:
```bash
python cli.py --config-check
```

Basic usage with mock data:
```bash
python cli.py --location "Tokyo" --interest "cybersecurity companies"
```

Enhanced mode with real APIs:
```bash
python cli.py --location "Tokyo" --interest "cybersecurity companies" --enhanced
```

Save results to file:
```bash
python cli.py --location "San Francisco" --interest "AI startups" --enhanced --output results.json
```

Verbose logging:
```bash
python cli.py --location "London" --interest "fintech" --enhanced --verbose
```

### Python API

#### Basic Workflow
```python
import asyncio
from target_exploit_workflow import TargetExploitWorkflow, WorkflowInput

async def main():
    workflow = TargetExploitWorkflow()
    
    workflow_input = WorkflowInput(
        location="Tokyo",
        interest="cybersecurity companies"
    )
    
    results = await workflow.execute(workflow_input)
    print(results)

asyncio.run(main())
```

#### Enhanced Workflow
```python
import asyncio
from enhanced_workflow import EnhancedTargetExploitWorkflow, WorkflowInput

async def main():
    workflow = EnhancedTargetExploitWorkflow()
    
    workflow_input = WorkflowInput(
        location="Tokyo", 
        interest="cybersecurity companies"
    )
    
    results = await workflow.execute(workflow_input)
    
    for result in results:
        print(f"Entity: {result.entity}")
        print(f"Link: {result.link}")
        print(f"Domain: {result.domain}")
        print()

asyncio.run(main())
```

## Workflow Steps

The workflow follows these main steps:

1. **Wikipedia Search**: Searches Wikipedia for general information about the topic
2. **Google Search**: Performs Google search for additional context
3. **LLM Knowledge Extraction**: Uses LLM to extract known entities from internal knowledge
4. **Entity Extraction**: Combines all sources to extract relevant entities using LLM
5. **Iterative Processing**: For each entity:
   - Performs targeted Google search
   - Extracts domain information using LLM
   - Processes and formats results

## Original DSL Structure

The original Dify workflow contained:

- **Start Node**: Input variables (location, interest)
- **Tool Nodes**: Wikipedia search, Google search (2 instances)
- **LLM Nodes**: Entity extraction, domain extraction, knowledge extraction
- **Template Transform Nodes**: Data formatting and transformation
- **Code Execution Nodes**: JSON parsing and domain extraction
- **Iteration Node**: Processing arrays of entities
- **End Node**: Final output

## Output Format

### Basic Workflow
Returns a list of domain strings:
```python
["example.com", "another-site.org", "third-domain.net"]
```

### Enhanced Workflow
Returns a list of EntityResult objects:
```python
[
    EntityResult(
        entity="Company Name",
        link="https://company.com",
        domain="company.com"
    ),
    ...
]
```

## Limitations

- Google Custom Search API has daily quotas and rate limits
- LLM responses may vary and require validation
- Some entities may not have discoverable official websites
- Mock mode provides sample data for testing without API access

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is provided as-is for educational and research purposes. Please ensure compliance with all applicable APIs' terms of service and rate limits.

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure all required API keys are set as environment variables
2. **Rate Limiting**: Google Custom Search has daily quotas - consider implementing delays
3. **Ollama Connection**: Ensure Ollama is running locally if using local LLM
4. **JSON Parsing Errors**: LLM responses may not always be valid JSON - error handling is included

### Debug Mode

Run with verbose logging to see detailed execution:
```bash
python cli.py --location "Tokyo" --interest "tech" --enhanced --verbose
```
