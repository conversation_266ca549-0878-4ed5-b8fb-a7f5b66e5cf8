#!/usr/bin/env python3
"""
Test script for CSV export functionality
"""

import asyncio
import logging
import os
import csv
from enhanced_workflow import EnhancedTargetExploitWorkflow
from target_exploit_workflow import WorkflowInput

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_csv_export():
    """Test the CSV export functionality"""
    print("=== CSV Export Test ===\n")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    # Test with cybersecurity companies in Tokyo
    workflow_input = WorkflowInput(
        location="Tokyo",
        interest="cybersecurity companies"
    )
    
    print(f"🔍 Running workflow for: {workflow_input.interest} in {workflow_input.location}")
    
    try:
        # Execute workflow
        results = await workflow.execute(workflow_input)
        
        if results:
            print(f"✓ Found {len(results)} entities")
            
            # Display summary of results
            print("\n📋 Results Summary:")
            for i, result in enumerate(results, 1):
                subdomain_count = len(result.subdomains) if result.subdomains else 0
                print(f"  {i}. {result.entity} - {subdomain_count} verified subdomains")
            
            # Test CSV export
            print("\n📄 Testing CSV Export...")
            
            # Export summary CSV
            summary_csv = workflow.export_results_to_csv(results, workflow_input)
            if summary_csv:
                print(f"✓ Summary CSV exported: {summary_csv}")
                await analyze_csv_file(summary_csv, "Summary")
            
            # Export detailed CSV
            detailed_csv = workflow.export_detailed_results_to_csv(results, workflow_input)
            if detailed_csv:
                print(f"✓ Detailed CSV exported: {detailed_csv}")
                await analyze_csv_file(detailed_csv, "Detailed")
            
            # Test custom filename
            custom_summary = workflow.export_results_to_csv(results, workflow_input, "custom_test_results")
            if custom_summary:
                print(f"✓ Custom filename CSV exported: {custom_summary}")
            
            print(f"\n📊 Export Summary:")
            total_subdomains = sum(len(r.subdomains or []) for r in results)
            print(f"  • Total entities: {len(results)}")
            print(f"  • Total verified subdomains: {total_subdomains}")
            print(f"  • Average subdomains per entity: {total_subdomains / len(results):.1f}")
            
        else:
            print("❌ No results found")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def analyze_csv_file(filepath: str, csv_type: str):
    """Analyze the contents of a CSV file"""
    try:
        if not os.path.exists(filepath):
            print(f"❌ CSV file not found: {filepath}")
            return
        
        with open(filepath, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            rows = list(reader)
            
            print(f"\n📊 {csv_type} CSV Analysis ({os.path.basename(filepath)}):")
            print(f"  • File size: {os.path.getsize(filepath)} bytes")
            print(f"  • Number of rows: {len(rows)}")
            print(f"  • Columns: {', '.join(reader.fieldnames)}")
            
            if rows:
                # Show first few rows as examples
                print(f"  • Sample data (first 2 rows):")
                for i, row in enumerate(rows[:2], 1):
                    print(f"    Row {i}:")
                    for key, value in row.items():
                        if len(str(value)) > 50:
                            value = str(value)[:47] + "..."
                        print(f"      {key}: {value}")
                
                # Statistics for summary CSV
                if csv_type == "Summary":
                    entities_with_subdomains = sum(1 for row in rows if int(row.get('verified_subdomains_count', 0)) > 0)
                    max_subdomains = max(int(row.get('verified_subdomains_count', 0)) for row in rows)
                    print(f"  • Entities with subdomains: {entities_with_subdomains}/{len(rows)}")
                    print(f"  • Max subdomains for single entity: {max_subdomains}")
                
                # Statistics for detailed CSV
                elif csv_type == "Detailed":
                    unique_entities = len(set(row.get('entity', '') for row in rows))
                    subdomain_types = {}
                    for row in rows:
                        subdomain_type = row.get('subdomain_type', 'unknown')
                        subdomain_types[subdomain_type] = subdomain_types.get(subdomain_type, 0) + 1
                    
                    print(f"  • Unique entities: {unique_entities}")
                    print(f"  • Subdomain types: {dict(subdomain_types)}")
            
    except Exception as e:
        print(f"❌ Error analyzing CSV file {filepath}: {e}")

async def test_csv_formats():
    """Test different CSV export formats and edge cases"""
    print("\n=== CSV Format Testing ===\n")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    # Test with different inputs
    test_cases = [
        {
            "name": "Simple case",
            "input": WorkflowInput(location="London", interest="fintech"),
            "description": "Test with simple location and interest"
        },
        {
            "name": "Special characters",
            "input": WorkflowInput(location="São Paulo", interest="AI/ML companies"),
            "description": "Test with special characters and slashes"
        }
    ]
    
    for test_case in test_cases:
        print(f"🧪 Testing: {test_case['name']}")
        print(f"   Description: {test_case['description']}")
        
        try:
            # Run a quick workflow (will use mock data)
            results = await workflow.execute(test_case['input'])
            
            if results:
                # Test CSV export with special characters
                csv_file = workflow.export_results_to_csv(results, test_case['input'])
                if csv_file:
                    print(f"   ✓ CSV exported successfully: {os.path.basename(csv_file)}")
                    
                    # Verify file can be read back
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        row_count = sum(1 for _ in reader)
                        print(f"   ✓ CSV readable with {row_count} rows")
                else:
                    print(f"   ❌ CSV export failed")
            else:
                print(f"   ⚠️ No results to export")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()

async def demonstrate_cli_usage():
    """Demonstrate CLI usage with CSV export"""
    print("=== CLI Usage Examples ===\n")
    
    print("💡 CLI Commands for CSV Export:")
    print()
    print("1. Basic CSV export:")
    print("   python cli.py --location Tokyo --interest 'cybersecurity companies' --enhanced --export-csv")
    print()
    print("2. CSV export with custom filename:")
    print("   python cli.py --location Tokyo --interest 'cybersecurity companies' --enhanced --export-csv --csv-filename my_results")
    print()
    print("3. Combined JSON and CSV export:")
    print("   python cli.py --location Tokyo --interest 'cybersecurity companies' --enhanced --export-csv --output results.json")
    print()
    print("📁 Output files will be created in the 'output/' directory:")
    print("   • Summary CSV: One row per entity with all subdomains in one field")
    print("   • Detailed CSV: One row per subdomain for detailed analysis")
    print()

async def main():
    """Run all CSV export tests"""
    await test_csv_export()
    await test_csv_formats()
    await demonstrate_cli_usage()
    
    print("="*60)
    print("🎉 CSV Export Feature Summary")
    print("="*60)
    print("✓ Added CSV export functionality to enhanced workflow")
    print("✓ Two CSV formats: Summary (one row per entity) and Detailed (one row per subdomain)")
    print("✓ Automatic filename generation with timestamps")
    print("✓ Custom filename support")
    print("✓ UTF-8 encoding for international characters")
    print("✓ Organized output in 'output/' directory")
    print("✓ CLI integration with --export-csv flag")
    print("✓ Error handling and graceful fallbacks")
    print("\nThe workflow now provides comprehensive CSV export for easy analysis and sharing!")

if __name__ == "__main__":
    asyncio.run(main())
