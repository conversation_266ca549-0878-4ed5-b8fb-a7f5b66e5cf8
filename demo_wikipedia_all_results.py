#!/usr/bin/env python3
"""
Simple demonstration of the updated EnhancedWikipediaSearchNode that returns all search results
"""

import asyncio
import logging
from enhanced_workflow import EnhancedWikipediaSearchNode

# Setup logging
logging.basicConfig(level=logging.WARNING)  # Reduce log noise for demo

async def demo_all_results():
    """Demonstrate the new all-results functionality"""
    print("=== Wikipedia All Results Demo ===\n")
    
    wiki_node = EnhancedWikipediaSearchNode()
    
    # Test case: Cybersecurity companies in Tokyo
    print("🔍 Searching for: 'cybersecurity companies' in 'Tokyo'")
    result = await wiki_node.execute({
        "interest": "cybersecurity companies", 
        "location": "Tokyo"
    })
    
    print(f"📊 Found {result.get('total_results', 0)} unique results:\n")
    
    for i, res in enumerate(result.get("results", []), 1):
        print(f"{i}. 📄 {res['title']}")
        print(f"   🔗 {res['url']}")
        print(f"   🏷️  Type: {res['type']} (from query: '{res['query']}')")
        print(f"   📝 {res['text'][:100]}...")
        print()
    
    print("=" * 60)
    print("🔄 Backward Compatibility:")
    print(f"   Primary result: {result.get('title', 'None')}")
    print(f"   Text length: {len(result.get('text', ''))} characters")
    
    print("\n" + "=" * 60)
    print("💡 How to use in your code:")
    print("""
# Get all results
all_results = result['results']
for res in all_results:
    title = res['title']
    content = res['text']
    url = res['url']
    result_type = res['type']  # 'direct', 'disambiguation', or 'search'
    
# Get primary result (backward compatible)
primary_content = result['text']
primary_title = result['title']
""")

if __name__ == "__main__":
    asyncio.run(demo_all_results())
