app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: target-exploit
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/ollama:0.0.3@9ded90ac00e8510119a24be7396ba77191c9610d5e1e29f59d68fa1229822fc7
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/wikipedia:0.0.3@c15c50172ae5abf681847180509a8df32d5944c3be3c91066d61c931b82b3470
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/google:0.1.1@190c6e5311fcf6ecca6541514bfddc21c95a50229e081066c221692907ca54fa
kind: app
version: 0.2.0
workflow:
  conversation_variables: []
  environment_variables:
  - description: ''
    id: 64b6b137-f1f3-414d-b6e3-cc37f9289ecb
    name: google_lan
    selector:
    - env
    - google_lan
    value: en
    value_type: string
  - description: ''
    id: 9e334b84-7b51-481d-a8fc-8f3b2bfe4bc9
    name: google_country
    selector:
    - env
    - google_country
    value: jp
    value_type: string
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: 1747357595711-source-1747620225282-target
      selected: false
      source: '1747357595711'
      sourceHandle: source
      target: '1747620225282'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 1747620225282-source-17476158901620-target
      selected: false
      source: '1747620225282'
      sourceHandle: source
      target: '17476158901620'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: code
      id: 1747625155858-source-1747625285578-target
      selected: false
      source: '1747625155858'
      sourceHandle: source
      target: '1747625285578'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        sourceType: llm
        targetType: template-transform
      id: 1747638146739-source-1747639494483-target
      selected: false
      source: '1747638146739'
      sourceHandle: source
      target: '1747639494483'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: 1747357595711-source-1747658166995-target
      selected: false
      source: '1747357595711'
      sourceHandle: source
      target: '1747658166995'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: template-transform
      id: 1747658166995-source-1747658446946-target
      selected: false
      source: '1747658166995'
      sourceHandle: source
      target: '1747658446946'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: llm
      id: 1747658446946-source-17476158901620-target
      selected: false
      source: '1747658446946'
      sourceHandle: source
      target: '17476158901620'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        sourceType: iteration-start
        targetType: tool
      id: 1747626477378start-source-1747658853097-target
      selected: false
      source: 1747626477378start
      sourceHandle: source
      target: '1747658853097'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        sourceType: tool
        targetType: template-transform
      id: 1747658853097-source-1747658871649-target
      selected: false
      source: '1747658853097'
      sourceHandle: source
      target: '1747658871649'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        sourceType: template-transform
        targetType: llm
      id: 1747658871649-source-1747638146739-target
      selected: false
      source: '1747658871649'
      sourceHandle: source
      target: '1747638146739'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: 17476158901620-source-1747625155858-target
      source: '17476158901620'
      sourceHandle: source
      target: '1747625155858'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1747357595711-source-1747812601228-target
      selected: false
      source: '1747357595711'
      sourceHandle: source
      target: '1747812601228'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: 1747812601228-source-1747814043414-target
      source: '1747812601228'
      sourceHandle: source
      target: '1747814043414'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: llm
      id: 1747814043414-source-17476158901620-target
      source: '1747814043414'
      sourceHandle: source
      target: '17476158901620'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: iteration
      id: 1747625285578-source-1747626477378-target
      selected: false
      source: '1747625285578'
      sourceHandle: source
      target: '1747626477378'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: iteration
        targetType: end
      id: 1747626477378-source-1747639544890-target
      source: '1747626477378'
      sourceHandle: source
      target: '1747639544890'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        sourceType: template-transform
        targetType: code
      id: 1747639494483-source-1749176245192-target
      source: '1747639494483'
      sourceHandle: source
      target: '1749176245192'
      targetHandle: target
      type: custom
      zIndex: 1002
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: location
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: location
        - label: interest
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: interest
      height: 116
      id: '1747357595711'
      position:
        x: 30
        y: 394.5
      positionAbsolute:
        x: 30
        y: 394.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            format: json
          mode: chat
          name: qwen3:32b
          provider: langgenius/ollama/ollama
        prompt_template:
        - edition_type: basic
          id: 14905f07-5c07-4595-a94e-13748fa52ea6
          role: system
          text: 'You are an OSINT assistant and now investing a targeted area. please
            list {{#1747357595711.interest#}} in {{#1747357595711.location#}} according
            to search result:{{#1747658446946.output#}}, {{#1747814043414.output#}}
            and Wikipedia page: {{#1747620225282.text#}}. You can identify and filter
            entities by snippet and title fields.


            ## Output

            - Do not output entities that are the same as already output.

            - Do not output historic entity.

            - Please output in json format.


            ```json

            entities: array[string] | None

            ```'
        - id: d4726e95-278c-4ab0-a763-effa47905107
          role: user
          text: ''
        retry_config:
          max_retries: 3
          retry_enabled: false
          retry_interval: 5000
        selected: false
        structured_output:
          schema:
            additionalProperties: false
            properties:
              Unit_Name:
                description: output only the name of the unit
                items:
                  type: string
                type: array
            required: []
            type: object
        structured_output_enabled: false
        title: 实体抽取
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '17476158901620'
      position:
        x: 944.5112261189358
        y: 394.5
      positionAbsolute:
        x: 944.5112261189358
        y: 394.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        default_value:
        - key: text
          type: string
          value: ''
        - key: json
          type: array[object]
          value: '[]'
        desc: ''
        error_strategy: default-value
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: key words for searching
            ja_JP: key words for searching
            pt_BR: key words for searching
            zh_Hans: 查询关键词
          label:
            en_US: Query string
            ja_JP: Query string
            pt_BR: Query string
            zh_Hans: 查询语句
          llm_description: key words for searching, this should be in the language
            of "language" parameter
          max: null
          min: null
          name: query
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: The language of the Wikipedia to be searched
            ja_JP: The language of the Wikipedia to be searched
            pt_BR: The language of the Wikipedia to be searched
            zh_Hans: 要搜索的维基百科语言
          label:
            en_US: Language
            ja_JP: Language
            pt_BR: Language
            zh_Hans: 语言
          llm_description: language of the wikipedia to be searched, only "de" for
            German, "en" for English, "fr" for French, "hi" for Hindi, "ja" for Japanese,
            "ko" for Korean, "pl" for Polish, "pt" for Portuguese, "ro" for Romanian,
            "uk" for Ukrainian, "vi" for Vietnamese, and "zh" for Chinese are supported
          max: null
          min: null
          name: language
          options:
          - label:
              en_US: German
              ja_JP: German
              pt_BR: German
              zh_Hans: 德语
            value: de
          - label:
              en_US: English
              ja_JP: English
              pt_BR: English
              zh_Hans: 英语
            value: en
          - label:
              en_US: French
              ja_JP: French
              pt_BR: French
              zh_Hans: 法语
            value: fr
          - label:
              en_US: Hindi
              ja_JP: Hindi
              pt_BR: Hindi
              zh_Hans: 印地语
            value: hi
          - label:
              en_US: Japanese
              ja_JP: Japanese
              pt_BR: Japanese
              zh_Hans: 日语
            value: ja
          - label:
              en_US: Korean
              ja_JP: Korean
              pt_BR: Korean
              zh_Hans: 韩语
            value: ko
          - label:
              en_US: Polish
              ja_JP: Polish
              pt_BR: Polish
              zh_Hans: 波兰语
            value: pl
          - label:
              en_US: Portuguese
              ja_JP: Portuguese
              pt_BR: Portuguese
              zh_Hans: 葡萄牙语
            value: pt
          - label:
              en_US: Romanian
              ja_JP: Romanian
              pt_BR: Romanian
              zh_Hans: 罗马尼亚语
            value: ro
          - label:
              en_US: Ukrainian
              ja_JP: Ukrainian
              pt_BR: Ukrainian
              zh_Hans: 乌克兰语
            value: uk
          - label:
              en_US: Vietnamese
              ja_JP: Vietnamese
              pt_BR: Vietnamese
              zh_Hans: 越南语
            value: vi
          - label:
              en_US: Chinese
              ja_JP: Chinese
              pt_BR: Chinese
              zh_Hans: 中文
            value: zh
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          language: ''
          query: ''
        provider_id: langgenius/wikipedia/wikipedia
        provider_name: langgenius/wikipedia/wikipedia
        provider_type: builtin
        retry_config:
          max_retries: 3
          retry_enabled: false
          retry_interval: 1000
        selected: false
        title: 维基百科搜索
        tool_configurations: {}
        tool_description: 一个用于执行维基百科搜索并提取片段和网页的工具。
        tool_label: 维基百科搜索
        tool_name: wikipedia_search
        tool_parameters:
          language:
            type: mixed
            value: English
          query:
            type: mixed
            value: '{{#1747357595711.interest#}}{{#1747357595711.location#}}'
        type: tool
      height: 90
      id: '1747620225282'
      position:
        x: 630.2704163089655
        y: 377.17752495416687
      positionAbsolute:
        x: 630.2704163089655
        y: 377.17752495416687
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: '{{ text }}'
        title: 大模型输出转换
        type: template-transform
        variables:
        - value_selector:
          - '17476158901620'
          - text
          variable: text
      height: 54
      id: '1747625155858'
      position:
        x: 1246
        y: 394.5
      positionAbsolute:
        x: 1246
        y: 394.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "\ndef main(text) -> dict:\n    res = json.loads(text)\n    return {\n\
          \        \"entities\": res.get('entities', []),\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          entities:
            children: null
            type: array[string]
        selected: false
        title: 代码执行
        type: code
        variables:
        - value_selector:
          - '1747625155858'
          - output
          variable: text
      height: 54
      id: '1747625285578'
      position:
        x: 1550
        y: 394.5
      positionAbsolute:
        x: 1550
        y: 394.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_handle_mode: terminated
        height: 369
        is_parallel: false
        iterator_selector:
        - '1747625285578'
        - entities
        output_selector:
        - '1747639494483'
        - output
        output_type: array[string]
        parallel_nums: 10
        selected: false
        start_node_id: 1747626477378start
        title: 迭代
        type: iteration
        width: 1461.934471251833
      height: 369
      id: '1747626477378'
      position:
        x: 2.72098381618639
        y: 719.3694724703862
      positionAbsolute:
        x: 2.72098381618639
        y: 719.3694724703862
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1462
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1747626477378start
      parentId: '1747626477378'
      position:
        x: 60
        y: 81
      positionAbsolute:
        x: 62.72098381618639
        y: 800.3694724703862
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        model:
          completion_params:
            format: json
          mode: chat
          name: qwq:latest
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: a6ab0ebb-0614-4790-9945-8941fee2e6d0
          role: system
          text: "You are an OSINT Researcher and need to find the domain name of the\
            \ entity {{#1747626477378.item#}} from the search results below:{{#1747658871649.output#}}.\
            \ \n\n## Task\n- Check if the entity is located in {{#1747357595711.location#}}\
            \ and related to {{#1747357595711.interest#}} according to search results.\
            \ If not, output entity name and empty link. \n- Find the most related\
            \ one website link of {{#1747626477378.item#}} in {{#1747357595711.location#}}\
            \ from the link field in search results.\n\n## Output\n- Output the entity\
            \ name and the only one official website domain\n- Output blank link if\
            \ there is no official website in search results\n- Please output in json\
            \ format\n\n```json\nentity: string | None\nlink: string | None\n```\n"
        retry_config:
          max_retries: '3'
          retry_enabled: false
          retry_interval: 5000
        selected: false
        structured_output:
          schema:
            additionalProperties: false
            properties: {}
            required: []
            type: object
        structured_output_enabled: false
        title: 实体域名抽取
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1747638146739'
      parentId: '1747626477378'
      position:
        x: 165.65514985235194
        y: 205.74510974739405
      positionAbsolute:
        x: 168.37613366853833
        y: 925.1145822177803
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        selected: false
        template: '{{ arg1 }}'
        title: 模板转换 4
        type: template-transform
        variables:
        - value_selector:
          - '1747638146739'
          - text
          variable: arg1
      height: 54
      id: '1747639494483'
      parentId: '1747626477378'
      position:
        x: 687.2687426156302
        y: 230.6543113414042
      positionAbsolute:
        x: 689.9897264318166
        y: 950.0237838117904
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1747626477378'
          - output
          variable: result
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1747639544890'
      position:
        x: 1743.7303450659253
        y: 633.8397519315852
      positionAbsolute:
        x: 1743.7303450659253
        y: 633.8397519315852
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: used for searching
            ja_JP: used for searching
            pt_BR: used for searching
            zh_Hans: 用于搜索网页内容
          label:
            en_US: Query string
            ja_JP: Query string
            pt_BR: Query string
            zh_Hans: 查询语句
          llm_description: key words for searching
          max: null
          min: null
          name: query
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Parameter defines the language to use for the Google search. It's
              a two-letter language code. (e.g., en for English, es for Spanish, or
              fr for French). Head to https://serpapi.com/google-languages for a full
              list of supported Google languages.
            ja_JP: Parameter defines the language to use for the Google search. It's
              a two-letter language code. (e.g., en for English, es for Spanish, or
              fr for French). Head to https://serpapi.com/google-languages for a full
              list of supported Google languages.
            pt_BR: Parameter defines the language to use for the Google search. It's
              a two-letter language code. (e.g., en for English, es for Spanish, or
              fr for French). Head to https://serpapi.com/google-languages for a full
              list of supported Google languages.
            zh_Hans: 此参数定义用于 Google 搜索的语言。它是一个两字母的语言代码。 (例如，en 代表英语，es 代表西班牙语，fr 代表法语)。请前往
              https://serpapi.com/google-languages 查看完整的支持 Google 语言列表。
          label:
            en_US: Language
            ja_JP: Language
            pt_BR: Language
            zh_Hans: 语言
          llm_description: Language
          max: null
          min: null
          name: hl
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Parameter defines the country to use for the Google search. It's
              a two-letter country code. (e.g., us for the United States, uk for United
              Kingdom, or fr for France). Head to the https://serpapi.com/google-countries
              for a full list of supported Google countries.
            ja_JP: Parameter defines the country to use for the Google search. It's
              a two-letter country code. (e.g., us for the United States, uk for United
              Kingdom, or fr for France). Head to the https://serpapi.com/google-countries
              for a full list of supported Google countries.
            pt_BR: Parameter defines the country to use for the Google search. It's
              a two-letter country code. (e.g., us for the United States, uk for United
              Kingdom, or fr for France). Head to the https://serpapi.com/google-countries
              for a full list of supported Google countries.
            zh_Hans: 此参数定义用于 Google 搜索的国家。它是一个两字母的国家代码。 (例如，us 代表美国，uk 代表英国，fr 代表法国)。请前往
              https://serpapi.com/google-countries 查看完整的支持 Google 国家列表。
          label:
            en_US: Country
            ja_JP: Country
            pt_BR: Country
            zh_Hans: 国家
          llm_description: Country
          max: null
          min: null
          name: gl
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Parameter defines from where you want the search to originate.
              If several locations match the location requested, we'll pick the most
              popular one. Head to the /locations.json API if you need more precise
              control. The location and uule parameters can't be used together. It
              is recommended to specify location at the city level in order to simulate
              a real user’s search. If location is omitted, the search may take on
              the location of the proxy.
            ja_JP: Parameter defines from where you want the search to originate.
              If several locations match the location requested, we'll pick the most
              popular one. Head to the /locations.json API if you need more precise
              control. The location and uule parameters can't be used together. It
              is recommended to specify location at the city level in order to simulate
              a real user’s search. If location is omitted, the search may take on
              the location of the proxy.
            pt_BR: Parameter defines from where you want the search to originate.
              If several locations match the location requested, we'll pick the most
              popular one. Head to the /locations.json API if you need more precise
              control. The location and uule parameters can't be used together. It
              is recommended to specify location at the city level in order to simulate
              a real user’s search. If location is omitted, the search may take on
              the location of the proxy.
            zh_Hans: 此参数定义搜索的位置。如果有多个位置与请求的位置匹配，我们将选择最受欢迎的一个。如果需要更精确的控制，请前往 /locations.json
              API。location 和 uule 参数不能同时使用。建议在城市级别指定位置，以模拟真实用户的搜索。如果省略位置，则搜索可能会采用代理的位置。
          label:
            en_US: Location
            ja_JP: Location
            pt_BR: Location
            zh_Hans: 位置
          llm_description: Location
          max: null
          min: null
          name: location
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          gl: ''
          hl: ''
          location: ''
          query: ''
        provider_id: langgenius/google/google
        provider_name: langgenius/google/google
        provider_type: builtin
        selected: true
        title: 谷歌搜索
        tool_configurations: {}
        tool_description: 一个用于执行 Google SERP 搜索并提取片段和网页的工具。输入应该是一个搜索查询。
        tool_label: 谷歌搜索
        tool_name: google_search
        tool_parameters:
          gl:
            type: mixed
            value: '{{#env.google_country#}}'
          hl:
            type: mixed
            value: '{{#env.google_lan#}}'
          num:
            type: mixed
            value: '100'
          query:
            type: mixed
            value: '{{#1747357595711.interest#}} {{#1747357595711.location#}}'
        type: tool
      height: 54
      id: '1747658166995'
      position:
        x: 341.92449957075877
        y: 488.5
      positionAbsolute:
        x: 341.92449957075877
        y: 488.5
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "{% for entry in arg1 %}\r\n{{ entry }}\r\n{% endfor %}"
        title: 模板转换 5
        type: template-transform
        variables:
        - value_selector:
          - '1747658166995'
          - json
          variable: arg1
      height: 54
      id: '1747658446946'
      position:
        x: 630.2704163089655
        y: 488.5
      positionAbsolute:
        x: 630.2704163089655
        y: 488.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        isInIteration: true
        isInLoop: false
        is_team_authorization: true
        iteration_id: '1747626477378'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: used for searching
            ja_JP: used for searching
            pt_BR: used for searching
            zh_Hans: 用于搜索网页内容
          label:
            en_US: Query string
            ja_JP: Query string
            pt_BR: Query string
            zh_Hans: 查询语句
          llm_description: key words for searching
          max: null
          min: null
          name: query
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Parameter defines the language to use for the Google search. It's
              a two-letter language code. (e.g., en for English, es for Spanish, or
              fr for French). Head to https://serpapi.com/google-languages for a full
              list of supported Google languages.
            ja_JP: Parameter defines the language to use for the Google search. It's
              a two-letter language code. (e.g., en for English, es for Spanish, or
              fr for French). Head to https://serpapi.com/google-languages for a full
              list of supported Google languages.
            pt_BR: Parameter defines the language to use for the Google search. It's
              a two-letter language code. (e.g., en for English, es for Spanish, or
              fr for French). Head to https://serpapi.com/google-languages for a full
              list of supported Google languages.
            zh_Hans: 此参数定义用于 Google 搜索的语言。它是一个两字母的语言代码。 (例如，en 代表英语，es 代表西班牙语，fr 代表法语)。请前往
              https://serpapi.com/google-languages 查看完整的支持 Google 语言列表。
          label:
            en_US: Language
            ja_JP: Language
            pt_BR: Language
            zh_Hans: 语言
          llm_description: Language
          max: null
          min: null
          name: hl
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Parameter defines the country to use for the Google search. It's
              a two-letter country code. (e.g., us for the United States, uk for United
              Kingdom, or fr for France). Head to the https://serpapi.com/google-countries
              for a full list of supported Google countries.
            ja_JP: Parameter defines the country to use for the Google search. It's
              a two-letter country code. (e.g., us for the United States, uk for United
              Kingdom, or fr for France). Head to the https://serpapi.com/google-countries
              for a full list of supported Google countries.
            pt_BR: Parameter defines the country to use for the Google search. It's
              a two-letter country code. (e.g., us for the United States, uk for United
              Kingdom, or fr for France). Head to the https://serpapi.com/google-countries
              for a full list of supported Google countries.
            zh_Hans: 此参数定义用于 Google 搜索的国家。它是一个两字母的国家代码。 (例如，us 代表美国，uk 代表英国，fr 代表法国)。请前往
              https://serpapi.com/google-countries 查看完整的支持 Google 国家列表。
          label:
            en_US: Country
            ja_JP: Country
            pt_BR: Country
            zh_Hans: 国家
          llm_description: Country
          max: null
          min: null
          name: gl
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Parameter defines from where you want the search to originate.
              If several locations match the location requested, we'll pick the most
              popular one. Head to the /locations.json API if you need more precise
              control. The location and uule parameters can't be used together. It
              is recommended to specify location at the city level in order to simulate
              a real user’s search. If location is omitted, the search may take on
              the location of the proxy.
            ja_JP: Parameter defines from where you want the search to originate.
              If several locations match the location requested, we'll pick the most
              popular one. Head to the /locations.json API if you need more precise
              control. The location and uule parameters can't be used together. It
              is recommended to specify location at the city level in order to simulate
              a real user’s search. If location is omitted, the search may take on
              the location of the proxy.
            pt_BR: Parameter defines from where you want the search to originate.
              If several locations match the location requested, we'll pick the most
              popular one. Head to the /locations.json API if you need more precise
              control. The location and uule parameters can't be used together. It
              is recommended to specify location at the city level in order to simulate
              a real user’s search. If location is omitted, the search may take on
              the location of the proxy.
            zh_Hans: 此参数定义搜索的位置。如果有多个位置与请求的位置匹配，我们将选择最受欢迎的一个。如果需要更精确的控制，请前往 /locations.json
              API。location 和 uule 参数不能同时使用。建议在城市级别指定位置，以模拟真实用户的搜索。如果省略位置，则搜索可能会采用代理的位置。
          label:
            en_US: Location
            ja_JP: Location
            pt_BR: Location
            zh_Hans: 位置
          llm_description: Location
          max: null
          min: null
          name: location
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          gl: ''
          hl: ''
          location: ''
          query: ''
        provider_id: langgenius/google/google
        provider_name: langgenius/google/google
        provider_type: builtin
        selected: false
        title: 谷歌搜索2
        tool_configurations: {}
        tool_description: 一个用于执行 Google SERP 搜索并提取片段和网页的工具。输入应该是一个搜索查询。
        tool_label: 谷歌搜索
        tool_name: google_search
        tool_parameters:
          gl:
            type: mixed
            value: '{{#env.google_country#}}'
          hl:
            type: mixed
            value: '{{#env.google_lan#}}'
          num:
            type: mixed
            value: '10'
          query:
            type: mixed
            value: '{{#1747626477378.item#}} {{#1747357595711.location#}}'
        type: tool
      height: 54
      id: '1747658853097'
      parentId: '1747626477378'
      position:
        x: 204
        y: 78
      positionAbsolute:
        x: 206.7209838161864
        y: 797.3694724703862
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        selected: false
        template: "{% for entry in arg1 %}\r\n{{ entry }}\r\n{% endfor %}"
        title: 模板转换 6
        type: template-transform
        variables:
        - value_selector:
          - '1747658853097'
          - json
          variable: arg1
      height: 54
      id: '1747658871649'
      parentId: '1747626477378'
      position:
        x: 508
        y: 78
      positionAbsolute:
        x: 510.7209838161864
        y: 797.3694724703862
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            format: json
          mode: chat
          name: qwq:latest
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: 06e6fb93-a51b-48a7-b16e-5f70fa851299
          role: system
          text: 'Give some important entities about{{#1747357595711.interest#}}in
            {{#1747357595711.location#}}, output as a list.


            ## Output

            - DO NOT output entities you are unsure of.

            - Please output in json format.


            ```json

            entities: array[string] | None

            ```


            '
        selected: false
        title: LLM知识
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1747812601228'
      position:
        x: 341.92449957075877
        y: 255.64396063716447
      positionAbsolute:
        x: 341.92449957075877
        y: 255.64396063716447
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: '{{ arg1 }}'
        title: 大模型输出转换
        type: template-transform
        variables:
        - value_selector:
          - '1747812601228'
          - text
          variable: arg1
      height: 54
      id: '1747814043414'
      position:
        x: 630.2704163089655
        y: 255.64396063716447
      positionAbsolute:
        x: 630.2704163089655
        y: 255.64396063716447
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "from urllib.parse import urlparse\n\ndef main(text) -> dict:\n    res\
          \ = json.loads(text)\n    link = res.get('link', '')\n    if link:\n   \
          \     domain = urlparse(link).netloc\n    else:\n        domain = ''\n \
          \   domain = domain.split('www.')[-1]\n    return {'result': domain}\n"
        code_language: python3
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1747626477378'
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 提取域名
        type: code
        variables:
        - value_selector:
          - '1747639494483'
          - output
          variable: text
      height: 54
      id: '1749176245192'
      parentId: '1747626477378'
      position:
        x: 1096.2862475809932
        y: 92.0745109747395
      positionAbsolute:
        x: 1099.0072313971796
        y: 811.4439834451257
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    viewport:
      x: 369.2945502881298
      y: 18.13595653844186
      zoom: 0.923655537541026
