# Serper API Integration

This document describes the integration of Serper API (serper.dev) as an alternative search provider for the dify2python workflow.

## Overview

The system now supports two search API providers:
1. **Serper API** (serper.dev) - **Recommended**
2. **SERP API** (serpapi.com) - Legacy support

## Why Serper API?

Serper API offers several advantages over SERP API:

- **Higher Free Tier**: 2,500 searches/month vs 100 searches/month
- **Better Performance**: Direct API without wrapper libraries
- **Simpler Integration**: Native async support with aiohttp
- **Cost Effective**: More generous pricing tiers
- **Reliable**: Consistent JSON response format

## Configuration

### Environment Variables

Set the Serper API key as an environment variable:

```bash
export SERPER_API_KEY="your_serper_api_key_here"
```

### API Key Priority

The system automatically selects the search provider based on available API keys:

1. **Serper API** (if `SERPER_API_KEY` is configured) - **Preferred**
2. **SERP API** (if `SERP_API_KEY` is configured) - **Fallback**
3. **Mock Data** (if no API keys are configured) - **Development**

## API Setup

### Getting a Serper API Key

1. Visit [serper.dev](https://serper.dev/)
2. Sign up for a free account
3. Navigate to your dashboard
4. Copy your API key
5. Set the `SERPER_API_KEY` environment variable

### Free Tier Limits

- **Free Plan**: 2,500 searches/month
- **Rate Limit**: 1 request per second
- **No Credit Card Required**: For free tier

## Implementation Details

### API Endpoint

```
POST https://google.serper.dev/search
```

### Request Format

```json
{
  "q": "search query",
  "num": 10,
  "gl": "jp",
  "hl": "en"
}
```

### Headers

```
X-API-KEY: your_api_key
Content-Type: application/json
```

### Response Format

The API returns results in the following format:

```json
{
  "organic": [
    {
      "title": "Page Title",
      "link": "https://example.com",
      "snippet": "Page description...",
      "displayedLink": "example.com"
    }
  ]
}
```

## Code Integration

### New Classes

1. **EnhancedSerperSearchNode**: Main search node for enhanced workflow
2. **SerperSearchNode**: Base search node for standard workflow

### Configuration Updates

- Added `SERPER_API_KEY` to `WorkflowConfig`
- Added `serper` configuration to `SEARCH_CONFIG`
- Updated validation logic to support multiple search providers

### Workflow Updates

- Modified `_setup_nodes()` to automatically select search provider
- Added logging to indicate which search provider is being used
- Maintained backward compatibility with existing SERP API integration

## Testing

### Test Script

Use the provided test script to verify Serper API integration:

```bash
python test_serper_api.py
```

### Test Features

- Direct API call testing
- Async integration testing
- Comparison between SERP and Serper APIs
- Error handling verification

### Example Test Output

```
=== Serper API Test ===
✓ Serper API Key configured: cf082554c8...
🔍 Testing search query: 'cybersecurity companies Tokyo'
✓ Search successful! Found 10 results:
```

## Migration Guide

### From SERP API to Serper API

1. **Get Serper API Key**: Sign up at serper.dev
2. **Set Environment Variable**: `export SERPER_API_KEY="your_key"`
3. **Test Integration**: Run `python test_serper_api.py`
4. **Verify Configuration**: Run `python cli.py --config-check`

### Keeping Both APIs

You can configure both APIs for redundancy:

```bash
export SERP_API_KEY="your_serp_key"
export SERPER_API_KEY="your_serper_key"
```

The system will prefer Serper API but fall back to SERP API if needed.

## Error Handling

The integration includes comprehensive error handling:

- **API Key Missing**: Falls back to mock data
- **Network Errors**: Graceful degradation with logging
- **Rate Limiting**: Proper error messages and fallback
- **Invalid Responses**: JSON parsing error handling

## Performance Considerations

- **Async Support**: Native async/await with aiohttp
- **Connection Pooling**: Efficient HTTP connection reuse
- **Timeout Handling**: Configurable request timeouts
- **Concurrent Requests**: Support for multiple simultaneous searches

## Monitoring and Logging

The integration provides detailed logging:

```python
logger.info("Using Serper API for search functionality")
logger.debug(f"Serper API query: {query}")
logger.debug(f"Serper API response: {len(results)} results")
```

## Troubleshooting

### Common Issues

1. **API Key Not Working**
   - Verify key is correctly set in environment
   - Check key validity on serper.dev dashboard
   - Ensure no extra spaces or quotes in key

2. **No Results Returned**
   - Check query format and parameters
   - Verify API quota hasn't been exceeded
   - Review network connectivity

3. **Rate Limiting**
   - Free tier: 1 request per second
   - Implement delays between requests if needed
   - Consider upgrading to paid plan for higher limits

### Debug Mode

Enable debug logging to see detailed API interactions:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

Potential improvements for the Serper API integration:

- **Caching**: Implement response caching to reduce API calls
- **Retry Logic**: Add exponential backoff for failed requests
- **Multiple Engines**: Support for Bing, DuckDuckGo via Serper
- **Advanced Parameters**: Location-based search, date filtering
- **Batch Processing**: Multiple queries in single request

## Support

For issues related to:
- **Serper API**: Contact <EMAIL>
- **Integration**: Create an issue in the project repository
- **Configuration**: Check the main README.md file
