#!/usr/bin/env python3
"""
Test script for the new LLM knowledge extraction formatting feature
"""

import asyncio
import json
from enhanced_workflow import EnhancedTargetExploitWorkflow, EnhancedLLMNode

async def test_knowledge_formatting():
    """Test the knowledge extraction formatting functionality"""
    print("Testing LLM Knowledge Extraction Formatting Feature")
    print("=" * 60)
    
    # Create workflow instance
    workflow = EnhancedTargetExploitWorkflow()
    
    # Test 1: Valid JSON response
    print("\n1. Testing valid JSON response:")
    valid_json_result = {
        "text": json.dumps({
            "entities": [
                "Tech Company A",
                "Software Startup B", 
                "AI Research Institute C",
                "Government Tech Department",
                "University Computer Science Dept"
            ]
        })
    }
    
    formatted_result = workflow._format_knowledge_extraction_result(valid_json_result)
    print(formatted_result)
    
    # Test 2: Invalid JSON response (malformed)
    print("\n2. Testing malformed JSON response:")
    invalid_json_result = {
        "text": '{"entities": ["Entity1", "Entity2", "Entity3"'  # Missing closing brackets
    }
    
    formatted_result = workflow._format_knowledge_extraction_result(invalid_json_result)
    print(formatted_result)
    
    # Test 3: Non-JSON response with list format
    print("\n3. Testing non-JSON response with list format:")
    list_format_result = {
        "text": """Here are the important entities:
        1. Major Tech Corporation
        2. Regional Software Company
        3. AI Startup Incubator
        - Government Innovation Lab
        * University Research Center"""
    }
    
    formatted_result = workflow._format_knowledge_extraction_result(list_format_result)
    print(formatted_result)
    
    # Test 4: Non-JSON response with array-like content
    print("\n4. Testing non-JSON response with array-like content:")
    array_like_result = {
        "text": 'The main entities are: ["Company X", "Organization Y", "Institute Z"]'
    }
    
    formatted_result = workflow._format_knowledge_extraction_result(array_like_result)
    print(formatted_result)
    
    # Test 5: Empty response
    print("\n5. Testing empty response:")
    empty_result = {"text": ""}
    
    formatted_result = workflow._format_knowledge_extraction_result(empty_result)
    print(formatted_result)
    
    # Test 6: Complex entity objects in JSON
    print("\n6. Testing complex entity objects in JSON:")
    complex_json_result = {
        "text": json.dumps({
            "entities": [
                {"name": "TechCorp Inc", "type": "corporation"},
                {"entity": "StartupHub", "location": "downtown"},
                "Simple Entity Name",
                {"name": "Research Lab", "field": "AI"}
            ]
        })
    }
    
    formatted_result = workflow._format_knowledge_extraction_result(complex_json_result)
    print(formatted_result)
    
    # Test 7: Test with actual LLM node (mock)
    print("\n7. Testing with actual LLM node (mock response):")
    knowledge_node = EnhancedLLMNode("test_knowledge", "Test Knowledge", "knowledge_extraction")
    
    test_inputs = {
        "location": "San Francisco",
        "interest": "artificial intelligence"
    }
    
    llm_result = await knowledge_node.execute(test_inputs)
    formatted_result = workflow._format_knowledge_extraction_result(llm_result)
    print(formatted_result)
    
    print("\n" + "=" * 60)
    print("Knowledge extraction formatting tests completed!")

if __name__ == "__main__":
    asyncio.run(test_knowledge_formatting())
