#!/usr/bin/env python3
"""
Test script for the new subdomain extraction feature
"""

import asyncio
import logging
from enhanced_workflow import EnhancedTargetExploitWorkflow
from target_exploit_workflow import WorkflowInput

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_subdomain_extraction():
    """Test the new subdomain extraction functionality"""
    print("=== Subdomain Extraction Test ===\n")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    test_cases = [
        {
            "name": "Cybersecurity companies in Tokyo",
            "input": WorkflowInput(location="Tokyo", interest="cybersecurity companies")
        },
        {
            "name": "Technology companies in San Francisco",
            "input": WorkflowInput(location="San Francisco", interest="technology companies")
        },
        {
            "name": "AI startups in London",
            "input": WorkflowInput(location="London", interest="AI startups")
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{'='*60}")
        print(f"Test {i}: {test_case['name']}")
        print(f"Location: {test_case['input'].location}")
        print(f"Interest: {test_case['input'].interest}")
        print('='*60)
        
        try:
            results = await workflow.execute(test_case['input'])
            
            if results:
                print(f"✓ Found {len(results)} entities with subdomain information:\n")
                
                for j, result in enumerate(results, 1):
                    print(f"{j}. Entity: {result.entity}")
                    print(f"   Primary Link: {result.link}")
                    print(f"   Primary Domain: {result.domain}")
                    
                    if result.subdomains:
                        print(f"   📡 All Subdomains Found ({len(result.subdomains)}):")
                        for subdomain in result.subdomains:
                            print(f"      • {subdomain}")
                    else:
                        print(f"   📡 Subdomains: None found")
                    print()
                
                # Summary statistics
                all_subdomains = set()
                for result in results:
                    if result.subdomains:
                        all_subdomains.update(result.subdomains)
                
                print(f"📊 Summary for {test_case['name']}:")
                print(f"   • Total entities: {len(results)}")
                print(f"   • Total unique subdomains: {len(all_subdomains)}")
                print(f"   • Average subdomains per entity: {sum(len(r.subdomains or []) for r in results) / len(results):.1f}")
                
            else:
                print("❌ No results found")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n")

async def test_subdomain_extraction_method():
    """Test the subdomain extraction method directly"""
    print("=== Direct Subdomain Extraction Method Test ===\n")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    # Mock search results with various domain patterns
    mock_search_results = [
        {"link": "https://www.example.com/page1", "title": "Example Page 1"},
        {"link": "https://blog.example.com/post", "title": "Blog Post"},
        {"link": "https://api.example.com/docs", "title": "API Documentation"},
        {"link": "https://subdomain.example.com/info", "title": "Subdomain Info"},
        {"link": "https://another-site.org/about", "title": "Another Site"},
        {"link": "https://www.another-site.org/contact", "title": "Contact Page"},
        {"link": "https://mail.google.com/", "title": "Gmail"},
        {"link": "https://drive.google.com/", "title": "Google Drive"},
        {"link": "https://docs.google.com/", "title": "Google Docs"},
        {"link": "invalid-url", "title": "Invalid URL"},
        {"link": "", "title": "Empty URL"},
    ]
    
    print("Mock search results:")
    for result in mock_search_results:
        print(f"  • {result['link']} - {result['title']}")
    
    subdomains = workflow._extract_all_subdomains(mock_search_results)
    
    print(f"\n📡 Extracted subdomains ({len(subdomains)}):")
    for subdomain in subdomains:
        print(f"  • {subdomain}")
    
    print(f"\n📊 Analysis:")
    print(f"  • Total input URLs: {len(mock_search_results)}")
    print(f"  • Valid URLs processed: {len([r for r in mock_search_results if r['link'] and r['link'].startswith('http')])}")
    print(f"  • Unique subdomains found: {len(subdomains)}")
    
    # Group by root domain
    root_domains = {}
    for subdomain in subdomains:
        parts = subdomain.split('.')
        if len(parts) >= 2:
            root = '.'.join(parts[-2:])
            if root not in root_domains:
                root_domains[root] = []
            root_domains[root].append(subdomain)
    
    print(f"\n🌐 Grouped by root domain:")
    for root, subs in root_domains.items():
        print(f"  • {root}: {', '.join(subs)}")

async def main():
    """Run all tests"""
    await test_subdomain_extraction_method()
    await test_subdomain_extraction()
    
    print("="*60)
    print("🎉 Subdomain Extraction Feature Summary")
    print("="*60)
    print("✓ Added subdomains field to EntityResult")
    print("✓ Implemented _extract_all_subdomains() method")
    print("✓ Extracts all unique subdomains from search results")
    print("✓ Handles www. prefix removal")
    print("✓ Includes both full subdomains and root domains")
    print("✓ Provides detailed logging and statistics")
    print("✓ Maintains backward compatibility")
    print("\nThe workflow now provides comprehensive subdomain intelligence!")

if __name__ == "__main__":
    asyncio.run(main())
