#!/usr/bin/env python3
"""
Test script for optimized entity extraction prompts
"""

import asyncio
import json
import logging
from config import PROMPTS, WorkflowConfig
from enhanced_workflow import EnhancedTargetExploitWorkflow

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_entity_extraction_prompt():
    """Test the optimized entity extraction prompt with sample data"""
    
    print("=" * 80)
    print("TESTING OPTIMIZED ENTITY EXTRACTION PROMPT")
    print("=" * 80)
    
    # Sample test data
    test_inputs = {
        "location": "Tokyo, Japan",
        "interest": "artificial intelligence",
        "search_results": """Title: Sony AI Research Division
Snippet: Sony's artificial intelligence research division focuses on machine learning and robotics in Tokyo
Link: https://ai.sony.com
---
Title: University of Tokyo AI Lab
Snippet: Leading AI research laboratory at the University of Tokyo, Japan
Link: https://www.u-tokyo.ac.jp/ai
---
Title: SoftBank Robotics
Snippet: SoftBank's robotics and AI division developing humanoid robots in Tokyo
Link: https://www.softbankrobotics.com
---
Title: RIKEN AIP Center
Snippet: RIKEN Advanced Intelligence Project center for AI research in Japan
Link: https://aip.riken.jp
---""",
        "llm_knowledge": """LLM Knowledge Extraction Results:
1. Sony Corporation AI Division
2. University of Tokyo
3. RIKEN Advanced Intelligence Project
4. SoftBank Group
5. Preferred Networks
6. Toyota Research Institute
7. NTT Communications AI Lab""",
        "wikipedia_text": """Wikipedia Search Results:
==================================================

1. Artificial Intelligence in Japan
----------------------------------
URL: https://en.wikipedia.org/wiki/AI_Japan
Japan has been a leader in AI research with major companies like Sony, Toyota, and SoftBank investing heavily in artificial intelligence technologies. The University of Tokyo and RIKEN are prominent research institutions."""
    }
    
    # Format the prompt
    prompt_template = PROMPTS["entity_extraction"]
    formatted_prompt = prompt_template.format(**test_inputs)
    
    print("\n" + "=" * 60)
    print("FORMATTED ENTITY EXTRACTION PROMPT:")
    print("=" * 60)
    print(formatted_prompt)
    
    # Test with workflow
    print("\n" + "=" * 60)
    print("TESTING WITH ENHANCED WORKFLOW:")
    print("=" * 60)
    
    workflow = EnhancedTargetExploitWorkflow()
    
    # Test entity extraction node
    entity_node = workflow.entity_extraction_node
    
    # Mock the LLM response for testing
    mock_response = {
        "text": json.dumps({
            "entities": [
                "Sony Corporation AI Division",
                "University of Tokyo",
                "RIKEN Advanced Intelligence Project", 
                "SoftBank Group",
                "Preferred Networks",
                "Toyota Research Institute",
                "NTT Communications"
            ]
        })
    }
    
    print("Mock LLM Response:")
    print(json.dumps(mock_response, indent=2))
    
    # Test parsing
    try:
        entities_data = json.loads(mock_response.get("text", "{}"))
        entities = entities_data.get("entities", [])
        print(f"\nSuccessfully extracted {len(entities)} entities:")
        for i, entity in enumerate(entities, 1):
            print(f"  {i}. {entity}")
    except json.JSONDecodeError as e:
        print(f"Error parsing response: {e}")

async def test_knowledge_extraction_prompt():
    """Test the optimized knowledge extraction prompt"""
    
    print("\n" + "=" * 80)
    print("TESTING OPTIMIZED KNOWLEDGE EXTRACTION PROMPT")
    print("=" * 80)
    
    test_inputs = {
        "location": "Singapore",
        "interest": "fintech"
    }
    
    prompt_template = PROMPTS["knowledge_extraction"]
    formatted_prompt = prompt_template.format(**test_inputs)
    
    print("FORMATTED KNOWLEDGE EXTRACTION PROMPT:")
    print("=" * 60)
    print(formatted_prompt)

async def test_domain_extraction_prompt():
    """Test the optimized domain extraction prompt"""
    
    print("\n" + "=" * 80)
    print("TESTING OPTIMIZED DOMAIN EXTRACTION PROMPT")
    print("=" * 80)
    
    test_inputs = {
        "entity": "Sony Corporation",
        "location": "Tokyo, Japan", 
        "interest": "artificial intelligence",
        "search_results": """Title: Sony Corporation Official Website
Snippet: Sony Corporation global website with information about products and services
Link: https://www.sony.com
---
Title: Sony AI Research
Snippet: Sony's artificial intelligence research division
Link: https://ai.sony.com
---
Title: Sony on LinkedIn
Snippet: Sony Corporation company profile on LinkedIn
Link: https://linkedin.com/company/sony
---"""
    }
    
    prompt_template = PROMPTS["domain_extraction"]
    formatted_prompt = prompt_template.format(**test_inputs)
    
    print("FORMATTED DOMAIN EXTRACTION PROMPT:")
    print("=" * 60)
    print(formatted_prompt)

async def main():
    """Run all prompt tests"""
    await test_entity_extraction_prompt()
    await test_knowledge_extraction_prompt()
    await test_domain_extraction_prompt()
    
    print("\n" + "=" * 80)
    print("PROMPT OPTIMIZATION TESTING COMPLETED")
    print("=" * 80)
    print("\nKey Improvements Made:")
    print("1. ✅ More detailed and specific instructions")
    print("2. ✅ Clear entity extraction criteria")
    print("3. ✅ Better data source utilization")
    print("4. ✅ Quality filters and guidelines")
    print("5. ✅ Improved output format specifications")
    print("6. ✅ Examples and context for better understanding")
    print("7. ✅ Geographic and industry relevance validation")

if __name__ == "__main__":
    asyncio.run(main())
