#!/usr/bin/env python3
"""
Test script for IP address resolution functionality
"""

import asyncio
import logging
from target_exploit_workflow import resolve_domain_ip, resolve_subdomains_ips, SubdomainInfo

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_single_domain_resolution():
    """Test resolving a single domain"""
    print("=== Testing Single Domain Resolution ===")
    
    test_domains = [
        "google.com",
        "github.com", 
        "nonexistent-domain-12345.com",
        "localhost"
    ]
    
    for domain in test_domains:
        print(f"\nResolving {domain}...")
        result = resolve_domain_ip(domain, timeout=5)
        
        if result.ip_address:
            print(f"✓ {domain} → {result.ip_address}")
        elif result.dns_error:
            print(f"❌ {domain} → Error: {result.dns_error}")
        else:
            print(f"❓ {domain} → Unknown status")

async def test_multiple_domains_resolution():
    """Test resolving multiple domains concurrently"""
    print("\n=== Testing Multiple Domains Resolution ===")
    
    test_domains = [
        "google.com",
        "github.com",
        "stackoverflow.com",
        "python.org",
        "nonexistent-domain-12345.com"
    ]
    
    print(f"Resolving {len(test_domains)} domains concurrently...")
    results = await resolve_subdomains_ips(test_domains, timeout=5)
    
    print(f"\nResults:")
    successful = 0
    for result in results:
        if result.ip_address:
            print(f"✓ {result.domain} → {result.ip_address}")
            successful += 1
        elif result.dns_error:
            print(f"❌ {result.domain} → Error: {result.dns_error}")
        else:
            print(f"❓ {result.domain} → Unknown status")
    
    print(f"\nSummary: {successful}/{len(test_domains)} domains resolved successfully")

async def test_subdomain_info_structure():
    """Test the SubdomainInfo data structure"""
    print("\n=== Testing SubdomainInfo Structure ===")
    
    # Test with successful resolution
    info1 = SubdomainInfo(domain="example.com", ip_address="*************")
    print(f"Success case: {info1}")
    
    # Test with DNS error
    info2 = SubdomainInfo(domain="invalid.domain", dns_error="NXDOMAIN")
    print(f"Error case: {info2}")
    
    # Test with no data
    info3 = SubdomainInfo(domain="unknown.domain")
    print(f"Unknown case: {info3}")

async def test_timeout_handling():
    """Test timeout handling"""
    print("\n=== Testing Timeout Handling ===")
    
    # Test with very short timeout
    print("Testing with 1 second timeout...")
    result = resolve_domain_ip("google.com", timeout=1)
    
    if result.ip_address:
        print(f"✓ Resolved within timeout: {result.ip_address}")
    elif "timeout" in (result.dns_error or "").lower():
        print(f"⏰ Timeout occurred as expected: {result.dns_error}")
    else:
        print(f"❓ Unexpected result: {result.dns_error}")

async def main():
    """Run all tests"""
    print("🧪 IP Address Resolution Feature Tests")
    print("=" * 50)
    
    try:
        await test_single_domain_resolution()
        await test_multiple_domains_resolution()
        await test_subdomain_info_structure()
        await test_timeout_handling()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed!")
        print("\n📋 Feature Summary:")
        print("✓ Single domain IP resolution")
        print("✓ Concurrent multiple domain resolution")
        print("✓ Error handling for invalid domains")
        print("✓ Timeout handling")
        print("✓ SubdomainInfo data structure")
        print("✓ Integration with dig command")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
