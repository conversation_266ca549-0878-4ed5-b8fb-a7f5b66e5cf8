#!/usr/bin/env python3
"""
Demonstration of strict both-criteria Wikipedia verification
"""

import asyncio
import logging
from enhanced_workflow import EnhancedWikipediaSearchNode, EnhancedLLMNode

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def demo_strict_verification():
    """Demonstrate the strict both-criteria verification"""
    print("=== Strict Both-Criteria Wikipedia Verification Demo ===\n")
    
    print("📋 VERIFICATION RULE:")
    print("   ✅ INCLUDE: Articles relevant to BOTH location AND field")
    print("   ❌ EXCLUDE: Articles relevant to only location OR only field")
    print("   ❌ EXCLUDE: Articles not relevant to either\n")
    
    # Create Wikipedia search node with verification
    wiki_node = EnhancedWikipediaSearchNode()
    verification_node = EnhancedLLMNode("wikipedia_verification", "Wikipedia Relevance Verification", "wikipedia_relevance_verification")
    wiki_node.set_verification_node(verification_node)
    
    # Test case
    location = "Tokyo"
    interest = "cybersecurity"
    
    print(f"🔍 Searching for: '{interest}' in '{location}'")
    print("⚡ LLM verification applied BEFORE fetching page details\n")
    
    result = await wiki_node.execute({
        "interest": interest, 
        "location": location
    })
    
    print(f"📊 FINAL RESULTS: {len(result)} articles passed strict verification\n")
    print("=" * 60)
    
    if result:
        for i, res in enumerate(result, 1):
            print(f"\n{i}. 📄 {res['title']}")
            print(f"   🔗 {res['url']}")
            print(f"   ✅ VERIFIED: Relevant to BOTH {location} AND {interest}")
            print(f"   📝 {res['text'][:150]}...")
    else:
        print("\n❌ No articles met the strict both-criteria requirement")
        print(f"   This means no articles were found that are relevant to BOTH {location} AND {interest}")
    
    print("\n" + "=" * 60)
    print("🎯 BENEFITS OF STRICT VERIFICATION:")
    print("   • Higher quality, more targeted results")
    print("   • Reduced noise from irrelevant content")
    print("   • More efficient processing (pre-filtering)")
    print("   • Better focus on intersection of location + field")

async def demo_comparison():
    """Show comparison between different verification approaches"""
    print("\n\n=== VERIFICATION APPROACH COMPARISON ===\n")
    
    # Simulate different approaches
    print("📊 SIMULATION OF DIFFERENT APPROACHES:")
    print("   🔴 No Verification: 50 articles (many irrelevant)")
    print("   🟡 Loose Verification: 35 articles (location OR field relevant)")
    print("   🟢 Strict Verification: 12 articles (location AND field relevant)")
    print("\n✅ Our implementation uses STRICT verification for best quality!")

async def main():
    """Run the demonstration"""
    try:
        await demo_strict_verification()
        await demo_comparison()
        print("\n🎉 Demo completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
