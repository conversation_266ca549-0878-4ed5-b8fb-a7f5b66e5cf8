#!/usr/bin/env python3
"""
Simple demonstration of CSV export functionality
"""

import asyncio
import os
import csv
from enhanced_workflow import EnhancedTargetExploitWorkflow
from target_exploit_workflow import WorkflowInput, EntityResult

async def demo_csv_export():
    """Demonstrate CSV export with mock data"""
    print("=== CSV Export Demo ===\n")
    
    # Create mock results to demonstrate CSV export
    mock_results = [
        EntityResult(
            entity="SentinelOne, Inc.",
            link="https://www.sentinelone.com",
            domain="sentinelone.com",
            subdomains=["sentinelone.com", "jp.sentinelone.com", "api.sentinelone.com", "support.sentinelone.com"]
        ),
        EntityResult(
            entity="Fujitsu Limited",
            link="https://www.fujitsu.com",
            domain="fujitsu.com",
            subdomains=["fujitsu.com", "global.fujitsu", "fujitsu-general.com"]
        ),
        EntityResult(
            entity="NTT Security",
            link="https://security.ntt",
            domain="security.ntt",
            subdomains=["security.ntt", "jp.security.ntt", "group.ntt", "nttdata.com"]
        ),
        EntityResult(
            entity="Example Corp",
            link="https://example.com",
            domain="example.com",
            subdomains=[]  # Test case with no subdomains
        )
    ]
    
    workflow_input = WorkflowInput(
        location="Tokyo",
        interest="cybersecurity companies"
    )
    
    workflow = EnhancedTargetExploitWorkflow()
    
    print(f"📋 Mock Results Summary:")
    for i, result in enumerate(mock_results, 1):
        subdomain_count = len(result.subdomains) if result.subdomains else 0
        print(f"  {i}. {result.entity} - {subdomain_count} verified subdomains")
    
    print(f"\n📄 Exporting to CSV files...")
    
    # Export summary CSV
    summary_csv = workflow.export_results_to_csv(mock_results, workflow_input, "demo_summary")
    if summary_csv:
        print(f"✓ Summary CSV exported: {summary_csv}")
        await show_csv_content(summary_csv, "Summary")
    
    # Export detailed CSV
    detailed_csv = workflow.export_detailed_results_to_csv(mock_results, workflow_input, "demo_detailed")
    if detailed_csv:
        print(f"✓ Detailed CSV exported: {detailed_csv}")
        await show_csv_content(detailed_csv, "Detailed")
    
    print(f"\n📊 Export Statistics:")
    total_entities = len(mock_results)
    total_subdomains = sum(len(r.subdomains or []) for r in mock_results)
    print(f"  • Total entities: {total_entities}")
    print(f"  • Total verified subdomains: {total_subdomains}")
    print(f"  • Average subdomains per entity: {total_subdomains / total_entities:.1f}")

async def show_csv_content(filepath: str, csv_type: str):
    """Display the content of a CSV file"""
    try:
        print(f"\n📊 {csv_type} CSV Content ({os.path.basename(filepath)}):")
        
        with open(filepath, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            rows = list(reader)
            
            print(f"  • Columns: {', '.join(reader.fieldnames)}")
            print(f"  • Rows: {len(rows)}")
            print(f"  • File size: {os.path.getsize(filepath)} bytes")
            
            if rows:
                print(f"\n  Sample rows:")
                for i, row in enumerate(rows[:2], 1):
                    print(f"    Row {i}:")
                    for key, value in row.items():
                        if len(str(value)) > 60:
                            value = str(value)[:57] + "..."
                        print(f"      {key}: {value}")
                    print()
    
    except Exception as e:
        print(f"❌ Error reading CSV file: {e}")

async def demonstrate_csv_formats():
    """Demonstrate the two CSV formats"""
    print("\n=== CSV Format Explanation ===\n")
    
    print("📄 Summary CSV Format:")
    print("  • One row per entity")
    print("  • All subdomains in a single field (semicolon-separated)")
    print("  • Includes subdomain count for quick analysis")
    print("  • Best for: Overview, reporting, executive summaries")
    print()
    
    print("📄 Detailed CSV Format:")
    print("  • One row per subdomain")
    print("  • Each subdomain gets its own row")
    print("  • Includes subdomain type (primary/verified)")
    print("  • Best for: Technical analysis, detailed investigation")
    print()
    
    print("🔧 CLI Usage Examples:")
    print("  # Basic CSV export")
    print("  python cli.py --location Tokyo --interest 'fintech' --enhanced --export-csv")
    print()
    print("  # Custom filename")
    print("  python cli.py --location Tokyo --interest 'fintech' --enhanced --export-csv --csv-filename my_results")
    print()
    print("  # Combined with JSON export")
    print("  python cli.py --location Tokyo --interest 'fintech' --enhanced --export-csv --output results.json")

async def main():
    """Run the CSV export demonstration"""
    await demo_csv_export()
    await demonstrate_csv_formats()
    
    print("\n" + "="*60)
    print("🎉 CSV Export Feature Summary")
    print("="*60)
    print("✓ Two CSV formats: Summary and Detailed")
    print("✓ Automatic timestamp-based filenames")
    print("✓ Custom filename support")
    print("✓ UTF-8 encoding for international characters")
    print("✓ Organized output in 'output/' directory")
    print("✓ CLI integration with --export-csv flag")
    print("✓ Handles entities with no subdomains gracefully")
    print("✓ Includes metadata (location, interest, timestamp)")
    print("\nPerfect for OSINT analysis, reporting, and data sharing!")

if __name__ == "__main__":
    asyncio.run(main())
