# LLM Subdomain Verification Feature

## Overview

Added a sophisticated LLM-based verification system to filter and classify subdomains extracted from entity search results. This ensures that only organizational domains are returned, filtering out public platforms and unrelated domains.

## Key Features

### 1. **LLM-Based Classification**
- Uses large language model to analyze each subdomain
- Classifies domains as ORGANIZATIONAL, PUBLIC, or UNCERTAIN
- Provides confidence levels (high, medium, low) and detailed reasoning
- Only returns ORGANIZATIONAL and UNCERTAIN domains (filters out PUBLIC)

### 2. **Intelligent Context Analysis**
- Analyzes domain name, page title, and content snippet
- Considers organizational relationships (subsidiaries, business units)
- Identifies official company domains vs. public platforms
- Recognizes partner/reseller relationships

### 3. **Comprehensive Filtering**
- **Filters OUT**: LinkedIn, Wikipedia, Bloomberg, news sites, job boards
- **Keeps**: Official company domains, subdomains, subsidiaries, API endpoints
- **Uncertain**: Partner sites, resellers, third-party platforms with company presence

## Implementation Details

### **New Configuration**
```python
# Added to config.py
"subdomain_verification": {
    "name": "qwen3:32b",
    "provider": "ollama",
    "format": "json"
}
```

### **New LLM Prompt**
Sophisticated prompt that instructs the LLM to:
- Analyze domain ownership and organizational relationship
- Classify domains with confidence levels
- Provide detailed reasoning for each classification
- Focus on cybersecurity and OSINT use cases

### **Enhanced Data Structure**
```python
# Updated EntityResult
@dataclass
class EntityResult:
    entity: Optional[str]
    link: Optional[str]
    domain: Optional[str]
    subdomains: Optional[List[str]] = None  # Now contains verified domains only
```

### **New Methods**
- `_extract_all_subdomains()`: Extracts domains with context (title, snippet, URL)
- `_verify_subdomains_with_llm()`: Uses LLM to verify organizational ownership
- Enhanced logging and error handling

## Test Results

### **Example 1: Fujitsu Limited**
**Input domains (6):**
- fujitsu.com, linkedin.com, en.wikipedia.org, global.fujitsu, fujitsu-general.com, bloomberg.com

**LLM-Verified organizational domains (3):**
- ✅ `fujitsu.com` (ORGANIZATIONAL, high) - Official company website
- ✅ `global.fujitsu` (ORGANIZATIONAL, high) - Official company website  
- ✅ `fujitsu-general.com` (UNCERTAIN, medium) - Possible subsidiary or partner

**Filtered out (3):** linkedin.com, en.wikipedia.org, bloomberg.com

### **Example 2: SentinelOne, Inc.**
**LLM-Verified organizational domains (8):**
- ✅ `sentinelone.com` (ORGANIZATIONAL, high) - Official company website
- ✅ `jp.sentinelone.com` (ORGANIZATIONAL, high) - Official Japanese subdomain
- ✅ `cn.teldevice.co.jp` (UNCERTAIN, low) - Partner/reseller in Japan
- ✅ `prtimes.jp` (UNCERTAIN, medium) - Press release platform
- ✅ `japan.zdnet.com` (UNCERTAIN, medium) - Company profile on tech news site

### **Example 3: Google**
**LLM-Verified organizational domains (6):**
- ✅ `google.com` (ORGANIZATIONAL, high) - Official Google domain
- ✅ `about.google` (ORGANIZATIONAL, high) - Official company information
- ✅ `blog.google` (ORGANIZATIONAL, high) - Official corporate blog
- ✅ `play.google.com` (ORGANIZATIONAL, high) - Google Play store
- ✅ `withgoogle.com` (ORGANIZATIONAL, high) - Company initiatives domain

## Benefits

### **1. Higher Quality Intelligence**
- Eliminates noise from public platforms
- Focuses on actionable organizational infrastructure
- Reduces false positives in OSINT analysis

### **2. Detailed Classification**
- Confidence levels help prioritize targets
- Reasoning provides context for analysis
- Distinguishes between official domains and partner sites

### **3. OSINT Use Cases**
- **Attack Surface Mapping**: Focus on actual organizational infrastructure
- **Digital Footprint Analysis**: Comprehensive view of organizational domains
- **Subsidiary Discovery**: Identifies related business units and subsidiaries
- **Penetration Testing**: Accurate scope definition

### **4. Robust Error Handling**
- Graceful fallback if LLM verification fails
- Detailed logging for debugging and analysis
- Maintains backward compatibility

## Usage Example

```python
# Execute workflow with LLM verification
workflow = EnhancedTargetExploitWorkflow()
results = await workflow.execute(WorkflowInput(
    location="Tokyo", 
    interest="cybersecurity companies"
))

# Access verified subdomains
for result in results:
    print(f"Entity: {result.entity}")
    print(f"Verified subdomains: {result.subdomains}")
    # Only contains organizational/uncertain domains
```

## Performance Impact

- **Slightly slower** due to LLM verification calls
- **Much higher quality** results justify the performance cost
- **Configurable** - can disable verification if needed
- **Efficient** - processes all subdomains for an entity in one LLM call

## Configuration

The feature uses the existing LLM configuration and can work with:
- **Ollama** (local models like qwen3:32b)
- **OpenAI** (GPT models)
- **Mock mode** (for testing without LLM)

## Summary

The LLM subdomain verification feature transforms raw subdomain extraction into high-quality, actionable intelligence by:

1. ✅ **Filtering out public platforms** (LinkedIn, Wikipedia, news sites)
2. ✅ **Identifying organizational infrastructure** (official domains, subdomains, APIs)
3. ✅ **Recognizing business relationships** (subsidiaries, partners, resellers)
4. ✅ **Providing confidence levels** and detailed reasoning
5. ✅ **Maintaining backward compatibility** with existing workflows

This significantly improves the quality and actionability of OSINT intelligence gathered by the workflow.
