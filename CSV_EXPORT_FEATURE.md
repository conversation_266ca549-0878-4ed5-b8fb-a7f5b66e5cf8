# CSV Export Feature

## Overview

Added comprehensive CSV export functionality to the enhanced workflow, enabling easy analysis, reporting, and sharing of OSINT results. The feature provides two CSV formats optimized for different use cases.

## Key Features

### 1. **Two CSV Export Formats**

#### **Summary CSV** (One row per entity)
- **Purpose**: Overview, reporting, executive summaries
- **Structure**: One row per entity with all subdomains in a single field
- **Best for**: Quick analysis, dashboards, high-level reporting

#### **Detailed CSV** (One row per subdomain)
- **Purpose**: Technical analysis, detailed investigation
- **Structure**: One row per subdomain with individual metadata
- **Best for**: Deep analysis, filtering, technical investigations

### 2. **Automatic File Management**
- **Output Directory**: Creates `output/` directory automatically
- **Timestamp Filenames**: Auto-generates unique filenames with timestamps
- **Custom Filenames**: Support for user-specified filenames
- **UTF-8 Encoding**: Handles international characters properly

### 3. **CLI Integration**
- **`--export-csv`**: Enable CSV export
- **`--csv-filename`**: Specify custom filename
- **Combined Export**: Works with existing `--output` JSON export

## CSV Formats

### **Summary CSV Structure**
```csv
entity,primary_link,primary_domain,verified_subdomains_count,verified_subdomains,location,interest,timestamp
"SentinelOne, Inc.",https://www.sentinelone.com,sentinelone.com,4,"sentinelone.com; jp.sentinelone.com; api.sentinelone.com; support.sentinelone.com",Tokyo,cybersecurity companies,2025-06-27T05:42:48.142970
```

**Columns:**
- `entity`: Organization name
- `primary_link`: Main website URL
- `primary_domain`: Primary domain
- `verified_subdomains_count`: Number of verified subdomains
- `verified_subdomains`: Semicolon-separated list of all verified subdomains
- `location`: Target location from input
- `interest`: Area of interest from input
- `timestamp`: Export timestamp (ISO format)

### **Detailed CSV Structure**
```csv
entity,primary_link,primary_domain,subdomain,subdomain_type,location,interest,timestamp
"SentinelOne, Inc.",https://www.sentinelone.com,sentinelone.com,sentinelone.com,primary,Tokyo,cybersecurity companies,2025-06-27T05:42:48.143542
"SentinelOne, Inc.",https://www.sentinelone.com,sentinelone.com,jp.sentinelone.com,verified,Tokyo,cybersecurity companies,2025-06-27T05:42:48.143542
```

**Columns:**
- `entity`: Organization name
- `primary_link`: Main website URL
- `primary_domain`: Primary domain
- `subdomain`: Individual subdomain
- `subdomain_type`: `primary` (main domain) or `verified` (LLM-verified subdomain)
- `location`: Target location from input
- `interest`: Area of interest from input
- `timestamp`: Export timestamp (ISO format)

## Implementation Details

### **Core Methods**
```python
# Summary export (one row per entity)
def export_results_to_csv(self, results: List[EntityResult], workflow_input: WorkflowInput, filename: str = None) -> str

# Detailed export (one row per subdomain)
def export_detailed_results_to_csv(self, results: List[EntityResult], workflow_input: WorkflowInput, filename: str = None) -> str
```

### **Filename Generation**
```python
# Auto-generated filename format
"osint_results_{location}_{interest}_{timestamp}.csv"
"osint_detailed_results_{location}_{interest}_{timestamp}.csv"

# Example
"osint_results_Tokyo_cybersecurity_companies_20250627_054248.csv"
```

### **Error Handling**
- Graceful handling of missing data (empty strings for None values)
- UTF-8 encoding for international characters
- Safe filename generation (replaces spaces and special characters)
- Detailed logging of export operations

## Usage Examples

### **CLI Usage**
```bash
# Basic CSV export
python cli.py --location Tokyo --interest "cybersecurity companies" --enhanced --export-csv

# Custom filename
python cli.py --location Tokyo --interest "fintech" --enhanced --export-csv --csv-filename my_results

# Combined JSON and CSV export
python cli.py --location Tokyo --interest "fintech" --enhanced --export-csv --output results.json
```

### **Programmatic Usage**
```python
from enhanced_workflow import EnhancedTargetExploitWorkflow
from target_exploit_workflow import WorkflowInput

workflow = EnhancedTargetExploitWorkflow()
workflow_input = WorkflowInput(location="Tokyo", interest="cybersecurity companies")

# Execute workflow
results = await workflow.execute(workflow_input)

# Export to CSV
summary_csv = workflow.export_results_to_csv(results, workflow_input)
detailed_csv = workflow.export_detailed_results_to_csv(results, workflow_input)
```

## Use Cases

### **1. Executive Reporting**
- **Format**: Summary CSV
- **Purpose**: High-level overview of discovered entities
- **Features**: Entity count, subdomain statistics, quick insights

### **2. Technical Analysis**
- **Format**: Detailed CSV
- **Purpose**: Deep dive into individual subdomains
- **Features**: Subdomain-level filtering, technical investigation

### **3. Data Integration**
- **Format**: Both formats
- **Purpose**: Import into other tools (Excel, databases, BI tools)
- **Features**: Standard CSV format, UTF-8 encoding

### **4. Compliance Reporting**
- **Format**: Summary CSV with timestamps
- **Purpose**: Audit trails, compliance documentation
- **Features**: Timestamped exports, metadata tracking

## Benefits

### **1. Easy Analysis**
- Import into Excel, Google Sheets, or BI tools
- Filter and sort by any column
- Create charts and visualizations

### **2. Data Sharing**
- Standard CSV format for universal compatibility
- Lightweight files for easy sharing
- No proprietary formats or dependencies

### **3. Automation Integration**
- Machine-readable format for automated processing
- Consistent structure for scripting
- Timestamp tracking for data lineage

### **4. Scalability**
- Handles large datasets efficiently
- Minimal memory footprint
- Fast export operations

## File Organization

```
output/
├── osint_results_Tokyo_cybersecurity_companies_20250627_054248.csv
├── osint_detailed_results_Tokyo_cybersecurity_companies_20250627_054248.csv
├── custom_filename.csv
└── custom_filename_detailed.csv
```

## Statistics Example

From the demo export:
- **4 entities** processed
- **11 total verified subdomains** found
- **2.8 average subdomains** per entity
- **Summary CSV**: 4 rows, 744 bytes
- **Detailed CSV**: 12 rows, 1,723 bytes

## Summary

The CSV export feature provides:
✓ **Two optimized formats** for different analysis needs
✓ **Automatic file management** with organized output
✓ **CLI integration** with flexible options
✓ **UTF-8 encoding** for international support
✓ **Timestamp tracking** for audit trails
✓ **Error handling** with graceful fallbacks

Perfect for OSINT analysis, reporting, compliance, and data sharing workflows!
