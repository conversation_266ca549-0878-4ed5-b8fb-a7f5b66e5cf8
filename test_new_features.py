#!/usr/bin/env python3
"""
Test script for the new location/field verification and persistence features
"""

import asyncio
import os
import tempfile
from enhanced_workflow import EnhancedTargetExploitWorkflow
from target_exploit_workflow import WorkflowInput, EntityResult, SubdomainInfo


async def test_entity_location_verification():
    """Test entity location and field verification"""
    print("=== Testing Entity Location Verification ===")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    # Test the verification method directly
    test_entity = "Test Corp"
    test_location = "Tokyo"
    test_interest = "fintech"
    test_search_results = """
    Title: Test Corp - Financial Services in Tokyo
    Snippet: Test Corp provides fintech solutions in Tokyo, Japan
    Link: https://testcorp.com
    ---
    """
    
    verification_result = await workflow._verify_entity_location_and_field(
        test_entity, test_location, test_interest, test_search_results
    )
    
    print(f"Entity: {test_entity}")
    print(f"Location: {test_location}")
    print(f"Interest: {test_interest}")
    print(f"Verification Result: {verification_result}")
    print("✓ Entity location verification test completed\n")


async def test_subdomain_location_verification():
    """Test subdomain location and field verification"""
    print("=== Testing Subdomain Location Verification ===")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    # Test subdomain verification
    test_entity = "Test Corp"
    test_location = "Tokyo"
    test_interest = "fintech"
    test_subdomains = [
        {
            "domain": "api.testcorp.com",
            "title": "Test Corp API - Financial Services",
            "snippet": "API for financial services in Tokyo",
            "url": "https://api.testcorp.com"
        },
        {
            "domain": "tokyo.testcorp.com",
            "title": "Test Corp Tokyo Office",
            "snippet": "Tokyo regional office for fintech operations",
            "url": "https://tokyo.testcorp.com"
        }
    ]
    
    verification_result = await workflow._verify_subdomains_location_and_field(
        test_entity, test_location, test_interest, test_subdomains
    )
    
    print(f"Entity: {test_entity}")
    print(f"Subdomains tested: {len(test_subdomains)}")
    print(f"Verification Results: {verification_result}")
    print("✓ Subdomain location verification test completed\n")


def test_entity_persistence():
    """Test entity persistence functionality"""
    print("=== Testing Entity Persistence ===")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    # Create test entities
    test_entities = [
        EntityResult(
            entity="Test Corp 1",
            link="https://testcorp1.com",
            domain="testcorp1.com",
            subdomains=["api.testcorp1.com", "support.testcorp1.com"],
            subdomain_ips=[
                SubdomainInfo(domain="api.testcorp1.com", ip_address="***********"),
                SubdomainInfo(domain="support.testcorp1.com", ip_address="***********")
            ],
            location_verification={
                "entity": "Test Corp 1",
                "location_relevance": "relevant",
                "field_relevance": "relevant",
                "overall_classification": "RELEVANT",
                "confidence": "high",
                "location_evidence": "Operates in Tokyo",
                "field_evidence": "Fintech company",
                "recommendation": "include"
            },
            subdomain_location_verification=[
                {
                    "domain": "api.testcorp1.com",
                    "classification": "FIELD_RELEVANT",
                    "confidence": "high",
                    "purpose": "API services"
                }
            ]
        ),
        EntityResult(
            entity="Test Corp 2",
            link="https://testcorp2.com",
            domain="testcorp2.com",
            subdomains=["www.testcorp2.com"],
            location_verification={
                "entity": "Test Corp 2",
                "location_relevance": "relevant",
                "field_relevance": "relevant",
                "overall_classification": "RELEVANT",
                "confidence": "medium",
                "location_evidence": "Has Tokyo office",
                "field_evidence": "Financial services",
                "recommendation": "include"
            }
        )
    ]
    
    workflow_input = WorkflowInput(location="Tokyo", interest="fintech")
    
    # Test CSV export with verification data
    print("Testing CSV export with verification data...")
    csv_file = workflow.export_results_to_csv(test_entities, workflow_input, "test_export")
    if csv_file and os.path.exists(csv_file):
        print(f"✓ CSV export successful: {csv_file}")
        
        # Test loading entities from CSV
        print("Testing entity loading from CSV...")
        loaded_entities = workflow.load_entities_from_csv(csv_file)
        print(f"✓ Loaded {len(loaded_entities)} entities from CSV")
        
        # Verify loaded data
        if loaded_entities:
            first_entity = loaded_entities[0]
            print(f"  First entity: {first_entity.entity}")
            print(f"  Location verification: {first_entity.location_verification}")
            print(f"  Subdomain verification: {first_entity.subdomain_location_verification}")
        
        # Test merging entities
        print("Testing entity merging...")
        new_entities = [
            EntityResult(
                entity="Test Corp 3",
                link="https://testcorp3.com",
                domain="testcorp3.com"
            )
        ]
        
        merged_entities = workflow.merge_entities_with_existing(new_entities, loaded_entities)
        print(f"✓ Merged entities: {len(merged_entities)} total")
        
        # Clean up test file
        try:
            os.remove(csv_file)
            print("✓ Test file cleaned up")
        except:
            pass
    else:
        print("❌ CSV export failed")
    
    print("✓ Entity persistence test completed\n")


async def test_full_workflow_with_new_features():
    """Test the full workflow with new features using mock data"""
    print("=== Testing Full Workflow with New Features ===")
    
    workflow = EnhancedTargetExploitWorkflow()
    workflow_input = WorkflowInput(location="Tokyo", interest="fintech")
    
    print("Running workflow with mock data...")
    print("Note: This will use mock responses since no real API keys are configured")
    
    try:
        results = await workflow.execute(workflow_input)
        print(f"✓ Workflow completed successfully")
        print(f"✓ Found {len(results)} entities")
        
        if results:
            first_result = results[0]
            print(f"  First entity: {first_result.entity}")
            print(f"  Has location verification: {first_result.location_verification is not None}")
            print(f"  Has subdomain verification: {first_result.subdomain_location_verification is not None}")
            
            if first_result.location_verification:
                classification = first_result.location_verification.get('overall_classification', 'UNKNOWN')
                print(f"  Verification classification: {classification}")
    
    except Exception as e:
        print(f"❌ Workflow failed: {e}")
    
    print("✓ Full workflow test completed\n")


async def main():
    """Run all tests"""
    print("Testing New Location/Field Verification and Persistence Features")
    print("=" * 70)
    
    await test_entity_location_verification()
    await test_subdomain_location_verification()
    test_entity_persistence()
    await test_full_workflow_with_new_features()
    
    print("=" * 70)
    print("All tests completed!")
    print("\nTo test with real API integration:")
    print("1. Set up SERP_API_KEY and OLLAMA_BASE_URL in environment variables")
    print("2. Run: python cli.py --location 'Tokyo' --interest 'fintech' --enhanced --export-csv --save-persistent")


if __name__ == "__main__":
    asyncio.run(main())
