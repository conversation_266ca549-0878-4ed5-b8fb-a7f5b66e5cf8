#!/usr/bin/env python3
"""
Test script for markdown export functionality
"""

import asyncio
import os
import sys
from datetime import datetime
from target_exploit_workflow import <PERSON><PERSON>tyResult, SubdomainInfo, WorkflowInput
from enhanced_workflow import EnhancedTargetExploitWorkflow

# Import the markdown functions from cli
sys.path.append('.')
from cli import save_results_to_markdown, _generate_markdown_content


class MockArgs:
    """Mock arguments class for testing"""
    def __init__(self):
        self.location = "Tokyo"
        self.interest = "cybersecurity companies"
        self.enhanced = True
        self.markdown_filename = None


async def test_markdown_export():
    """Test the markdown export functionality"""
    print("=== Testing Markdown Export Functionality ===\n")
    
    # Create mock results similar to enhanced workflow output
    mock_results = [
        {
            "entity": "SentinelOne, Inc.",
            "link": "https://www.sentinelone.com",
            "domain": "sentinelone.com",
            "subdomains": ["sentinelone.com", "jp.sentinelone.com", "api.sentinelone.com", "support.sentinelone.com"],
            "subdomain_ips": [
                {
                    "domain": "api.sentinelone.com",
                    "ip_address": "************",
                    "dns_error": ""
                },
                {
                    "domain": "support.sentinelone.com",
                    "ip_address": "************",
                    "dns_error": ""
                }
            ],
            "location_verification": {
                "entity": "SentinelOne, Inc.",
                "location_relevance": "relevant",
                "field_relevance": "relevant",
                "overall_classification": "RELEVANT",
                "confidence": "high",
                "location_evidence": "Has offices in Tokyo and serves Japanese market",
                "field_evidence": "Leading cybersecurity company specializing in endpoint protection",
                "recommendation": "include"
            },
            "subdomain_location_verification": [
                {
                    "domain": "jp.sentinelone.com",
                    "classification": "FIELD_RELEVANT",
                    "confidence": "high",
                    "purpose": "Japan-specific website and services"
                },
                {
                    "domain": "api.sentinelone.com",
                    "classification": "TECHNICAL",
                    "confidence": "medium",
                    "purpose": "API services for platform integration"
                }
            ]
        },
        {
            "entity": "Fujitsu Limited",
            "link": "https://www.fujitsu.com",
            "domain": "fujitsu.com",
            "subdomains": ["fujitsu.com", "global.fujitsu.com", "security.fujitsu.com"],
            "subdomain_ips": [
                {
                    "domain": "security.fujitsu.com",
                    "ip_address": "************",
                    "dns_error": ""
                }
            ],
            "location_verification": {
                "entity": "Fujitsu Limited",
                "location_relevance": "relevant",
                "field_relevance": "partially_relevant",
                "overall_classification": "PARTIALLY_RELEVANT",
                "confidence": "medium",
                "location_evidence": "Japanese multinational company headquartered in Tokyo",
                "field_evidence": "Technology company with cybersecurity division",
                "recommendation": "include"
            },
            "subdomain_location_verification": [
                {
                    "domain": "security.fujitsu.com",
                    "classification": "FIELD_RELEVANT",
                    "confidence": "high",
                    "purpose": "Cybersecurity products and services"
                }
            ]
        },
        {
            "entity": "Example Corp",
            "link": "https://example.com",
            "domain": "example.com",
            "subdomains": [],
            "subdomain_ips": [],
            "location_verification": {
                "entity": "Example Corp",
                "location_relevance": "not_relevant",
                "field_relevance": "not_relevant",
                "overall_classification": "NOT_RELEVANT",
                "confidence": "high",
                "location_evidence": "No presence in Tokyo",
                "field_evidence": "Not related to cybersecurity",
                "recommendation": "exclude"
            },
            "subdomain_location_verification": []
        }
    ]
    
    # Test markdown content generation
    print("1. Testing markdown content generation...")
    args = MockArgs()
    markdown_content = _generate_markdown_content(mock_results, args)
    
    print(f"✓ Generated {len(markdown_content.split('\\n'))} lines of markdown content")
    print(f"✓ Content includes header, summary, and {len(mock_results)} entities")
    
    # Test file export with default filename
    print("\\n2. Testing markdown file export with default filename...")
    output_file = save_results_to_markdown(mock_results, args)
    
    if output_file and os.path.exists(output_file):
        file_size = os.path.getsize(output_file)
        print(f"✓ Markdown file created: {output_file}")
        print(f"✓ File size: {file_size} bytes")
        
        # Read and display first few lines
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:10]
            print(f"✓ First 10 lines preview:")
            for i, line in enumerate(lines, 1):
                print(f"   {i:2d}: {line.rstrip()}")
    else:
        print("❌ Failed to create markdown file")
    
    # Test file export with custom filename
    print("\\n3. Testing markdown file export with custom filename...")
    args.markdown_filename = "test_custom_report"
    custom_output_file = save_results_to_markdown(mock_results, args)
    
    if custom_output_file and os.path.exists(custom_output_file):
        print(f"✓ Custom markdown file created: {custom_output_file}")
        print(f"✓ File size: {os.path.getsize(custom_output_file)} bytes")
    else:
        print("❌ Failed to create custom markdown file")
    
    # Test with basic (non-enhanced) results
    print("\\n4. Testing with basic results...")
    basic_results = [
        "Entity 1: Basic result string",
        "Entity 2: Another basic result",
        {"simple": "dict result"}
    ]
    
    args.enhanced = False
    basic_markdown = _generate_markdown_content(basic_results, args)
    print(f"✓ Generated basic markdown with {len(basic_markdown.split('\\n'))} lines")
    
    # Summary
    print("\\n=== Test Summary ===")
    print("✓ Markdown content generation: PASSED")
    print("✓ Default filename export: PASSED")
    print("✓ Custom filename export: PASSED")
    print("✓ Basic results handling: PASSED")
    print("\\n🎉 All markdown export tests completed successfully!")
    
    # Show file locations
    print("\\n📁 Generated files:")
    if output_file:
        print(f"  • {output_file}")
    if custom_output_file:
        print(f"  • {custom_output_file}")


if __name__ == "__main__":
    asyncio.run(test_markdown_export())
