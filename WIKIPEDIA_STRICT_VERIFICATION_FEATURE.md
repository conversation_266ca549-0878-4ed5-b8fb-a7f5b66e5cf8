# Wikipedia Strict Both-Criteria LLM Verification Feature

## Overview

The `EnhancedWikipediaSearchNode` now includes **strict both-criteria LLM verification** that filters Wikipedia search results to only include articles relevant to **BOTH** the specified location AND interest field. This verification happens **before** fetching detailed page content for maximum efficiency.

## Key Features

### 1. **Strict Both-Criteria Filtering**
- **INCLUDE**: Only articles relevant to BOTH location AND field
- **EXCLUDE**: Articles relevant to only location OR only field  
- **EXCLUDE**: Articles not relevant to either criterion
- **Quality Focus**: Higher precision, lower noise

### 2. **Pre-Filtering Efficiency**
- LLM verification applied to search titles **BEFORE** fetching page details
- Saves time and API calls by avoiding unnecessary page fetches
- Processes only verified-relevant content

### 3. **Intelligent Verification Logic**
```python
# Only include if ALL criteria are met:
if (classification == 'RELEVANT' and 
    location_relevance == 'relevant' and 
    field_relevance == 'relevant'):
    # Include article
else:
    # Exclude article
```

## Implementation Details

### **Enhanced Verification Method**
```python
async def _verify_search_title(self, title: str, location: str, interest: str) -> Dict[str, Any]:
    """Use LLM to verify if a Wikipedia search title is relevant before fetching details"""
```

### **Strict Filtering Logic**
```python
# Apply verification before fetching page details
for result_title in search_results:
    verification = await self._verify_search_title(result_title, location, interest)
    
    # Strict both-criteria check
    if (verification.get('overall_classification') == 'RELEVANT' and 
        verification.get('location_relevance') == 'relevant' and 
        verification.get('field_relevance') == 'relevant'):
        # Only then fetch page details
        page = wikipedia.page(result_title)
```

### **Updated Prompt Template**
The verification prompt now emphasizes:
- **STRICT REQUIREMENT**: Must be relevant to BOTH criteria
- **Clear Guidelines**: Only "RELEVANT" classification should be included
- **Decision Rules**: When in doubt, exclude rather than include

## Configuration

### **Model Configuration** (config.py)
```python
"wikipedia_relevance_verification": {
    "name": "qwen3:32b",
    "provider": "ollama", 
    "format": "json"
}
```

### **Workflow Integration** (enhanced_workflow.py)
```python
# Setup verification node
self.wikipedia_verification_node = EnhancedLLMNode(
    "wikipedia_verification", 
    "Wikipedia Relevance Verification", 
    "wikipedia_relevance_verification"
)

# Connect to Wikipedia node
self.wikipedia_node.set_verification_node(self.wikipedia_verification_node)
```

## Usage Example

```python
# Create Wikipedia node with strict verification
wiki_node = EnhancedWikipediaSearchNode()
verification_node = EnhancedLLMNode("wikipedia_verification", "Wikipedia Relevance Verification", "wikipedia_relevance_verification")
wiki_node.set_verification_node(verification_node)

# Execute search with strict filtering
result = await wiki_node.execute({
    "interest": "cybersecurity companies", 
    "location": "Tokyo"
})

# Only articles relevant to BOTH Tokyo AND cybersecurity companies are returned
```

## Benefits

### **1. Higher Quality Results**
- Eliminates articles relevant to only one criterion
- Focuses on intersection of location + field
- Reduces noise and irrelevant content

### **2. Improved Efficiency**
- Pre-filtering saves API calls and processing time
- Only fetches page details for verified-relevant titles
- Faster overall execution

### **3. Better OSINT Analysis**
- More targeted intelligence gathering
- Higher signal-to-noise ratio
- Better focus on specific geographic + domain intersection

## Verification Classifications

| Classification | Location | Field | Include? | Example |
|---------------|----------|-------|----------|---------|
| **RELEVANT** | ✅ | ✅ | ✅ YES | "Tokyo Cybersecurity Companies" |
| **LOCATION_RELEVANT** | ✅ | ❌ | ❌ NO | "Tokyo Tourism" |
| **FIELD_RELEVANT** | ❌ | ✅ | ❌ NO | "Global Cybersecurity Trends" |
| **NOT_RELEVANT** | ❌ | ❌ | ❌ NO | "Ancient History" |

## Testing

Run the test scripts to see the feature in action:

```bash
# Test strict verification
python test_wikipedia_verification.py

# Demo the feature
python demo_strict_verification.py
```

## Logging Output

The feature provides detailed logging:
```
[INFO] Pre-filtering 15 search titles with LLM verification
[INFO] [1/15] Verifying title: Tokyo Cybersecurity Alliance
[INFO] ✓ Title approved: Tokyo Cybersecurity Alliance (BOTH location and field relevant)
[INFO] [2/15] Verifying title: Tokyo Tourism Board  
[INFO] ✗ Title filtered out: Tokyo Tourism Board (classification: LOCATION_RELEVANT, location: relevant, field: not_relevant)
[INFO] After title verification: 4/15 titles approved
[INFO] [1/4] Fetching page details for: Tokyo Cybersecurity Alliance
```

## Future Enhancements

Potential improvements:
1. Configurable strictness levels
2. Weighted scoring for partial relevance
3. Context-aware verification
4. Performance metrics and analytics
5. Custom verification rules per use case
