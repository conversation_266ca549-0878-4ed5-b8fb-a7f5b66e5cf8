#!/usr/bin/env python3
"""
Test script for LLM-verified subdomain extraction feature
"""

import asyncio
import logging
from enhanced_workflow import EnhancedTargetExploitWorkflow
from target_exploit_workflow import WorkflowInput

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_llm_subdomain_verification():
    """Test the LLM-verified subdomain extraction functionality"""
    print("=== LLM-Verified Subdomain Extraction Test ===\n")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    test_cases = [
        {
            "name": "Cybersecurity companies in Tokyo",
            "input": WorkflowInput(location="Tokyo", interest="cybersecurity companies"),
            "description": "Test with cybersecurity companies to see how LLM filters organizational vs public domains"
        },
        {
            "name": "Technology companies in Silicon Valley",
            "input": WorkflowInput(location="Silicon Valley", interest="technology companies"),
            "description": "Test with tech companies to verify subdomain classification accuracy"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{'='*70}")
        print(f"Test {i}: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        print(f"Location: {test_case['input'].location}")
        print(f"Interest: {test_case['input'].interest}")
        print('='*70)
        
        try:
            results = await workflow.execute(test_case['input'])
            
            if results:
                print(f"✓ Found {len(results)} entities with LLM-verified subdomains:\n")
                
                total_verified_subdomains = 0
                
                for j, result in enumerate(results, 1):
                    print(f"{j}. 🏢 Entity: {result.entity}")
                    print(f"   🔗 Primary Link: {result.link}")
                    print(f"   🌐 Primary Domain: {result.domain}")
                    
                    if result.subdomains:
                        print(f"   ✅ LLM-Verified Organizational Subdomains ({len(result.subdomains)}):")
                        for subdomain in result.subdomains:
                            print(f"      • {subdomain}")
                        total_verified_subdomains += len(result.subdomains)
                    else:
                        print(f"   ❌ No organizational subdomains verified")
                    print()
                
                # Summary statistics
                print(f"📊 Summary for {test_case['name']}:")
                print(f"   • Total entities processed: {len(results)}")
                print(f"   • Total verified organizational subdomains: {total_verified_subdomains}")
                print(f"   • Average verified subdomains per entity: {total_verified_subdomains / len(results):.1f}")
                
                # Show entities with most subdomains
                entities_with_subdomains = [(r.entity, len(r.subdomains or [])) for r in results]
                entities_with_subdomains.sort(key=lambda x: x[1], reverse=True)
                
                print(f"   • Top entities by subdomain count:")
                for entity, count in entities_with_subdomains[:3]:
                    print(f"     - {entity}: {count} subdomains")
                
            else:
                print("❌ No results found")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n")

async def test_subdomain_verification_method():
    """Test the LLM subdomain verification method directly"""
    print("=== Direct LLM Subdomain Verification Test ===\n")
    
    workflow = EnhancedTargetExploitWorkflow()
    
    # Mock subdomains with context that should be filtered
    mock_subdomains_with_context = [
        {
            "domain": "fujitsu.com",
            "title": "Fujitsu Global - Technology Solutions",
            "snippet": "Fujitsu is a leading Japanese information and communication technology company...",
            "url": "https://www.fujitsu.com"
        },
        {
            "domain": "linkedin.com",
            "title": "Fujitsu | LinkedIn",
            "snippet": "Fujitsu | 2,847,392 followers on LinkedIn. We are Fujitsu...",
            "url": "https://www.linkedin.com/company/fujitsu"
        },
        {
            "domain": "en.wikipedia.org",
            "title": "Fujitsu - Wikipedia",
            "snippet": "Fujitsu Limited is a Japanese multinational information and communications technology equipment...",
            "url": "https://en.wikipedia.org/wiki/Fujitsu"
        },
        {
            "domain": "global.fujitsu",
            "title": "Fujitsu Global Portal",
            "snippet": "Access Fujitsu's global technology solutions and services...",
            "url": "https://global.fujitsu/"
        },
        {
            "domain": "fujitsu-general.com",
            "title": "Fujitsu General - Air Conditioning Solutions",
            "snippet": "Fujitsu General provides innovative air conditioning and HVAC solutions...",
            "url": "https://www.fujitsu-general.com"
        },
        {
            "domain": "bloomberg.com",
            "title": "Fujitsu Ltd (6702:Tokyo) Company Profile - Bloomberg",
            "snippet": "Fujitsu Ltd operates as an information and communication technology company...",
            "url": "https://www.bloomberg.com/quote/6702:JP"
        }
    ]
    
    print("🔍 Testing LLM verification with mock subdomains:")
    for item in mock_subdomains_with_context:
        print(f"  • {item['domain']} - {item['title']}")
    
    print(f"\n🤖 Calling LLM for subdomain verification...")
    
    verified_subdomains = await workflow._verify_subdomains_with_llm(
        entity="Fujitsu Limited",
        location="Tokyo", 
        subdomains_with_context=mock_subdomains_with_context
    )
    
    print(f"\n✅ LLM-Verified Organizational Subdomains ({len(verified_subdomains)}):")
    for subdomain in verified_subdomains:
        print(f"  • {subdomain}")
    
    print(f"\n📊 Verification Analysis:")
    print(f"  • Input domains: {len(mock_subdomains_with_context)}")
    print(f"  • Verified organizational domains: {len(verified_subdomains)}")
    print(f"  • Filtered out (public/unrelated): {len(mock_subdomains_with_context) - len(verified_subdomains)}")
    
    # Show what was likely filtered out
    all_input_domains = {item["domain"] for item in mock_subdomains_with_context}
    verified_set = set(verified_subdomains)
    filtered_out = all_input_domains - verified_set
    
    if filtered_out:
        print(f"  • Likely filtered domains: {', '.join(sorted(filtered_out))}")

async def demonstrate_verification_benefits():
    """Demonstrate the benefits of LLM verification"""
    print("=== LLM Verification Benefits Demo ===\n")
    
    print("🎯 Benefits of LLM Subdomain Verification:")
    print("1. ✅ Filters out public platforms (LinkedIn, Wikipedia, Bloomberg)")
    print("2. ✅ Identifies official organizational domains and subdomains")
    print("3. ✅ Recognizes subsidiaries and business units")
    print("4. ✅ Provides confidence levels and reasoning")
    print("5. ✅ Reduces false positives in OSINT intelligence")
    print("6. ✅ Focuses on actionable organizational infrastructure")
    
    print("\n🔍 Example Classifications:")
    print("• fujitsu.com → ORGANIZATIONAL (Official company website)")
    print("• api.fujitsu.com → ORGANIZATIONAL (Company API subdomain)")
    print("• fujitsu-general.com → ORGANIZATIONAL (Subsidiary company)")
    print("• linkedin.com/company/fujitsu → PUBLIC (Social media platform)")
    print("• en.wikipedia.org/wiki/Fujitsu → PUBLIC (Public encyclopedia)")
    print("• bloomberg.com/quote/6702:JP → PUBLIC (Financial news site)")
    
    print("\n💡 Use Cases:")
    print("• Attack surface mapping")
    print("• Digital footprint analysis")
    print("• Organizational infrastructure discovery")
    print("• Subsidiary and business unit identification")
    print("• Focused penetration testing scope")

async def main():
    """Run all tests"""
    await test_subdomain_verification_method()
    await test_llm_subdomain_verification()
    await demonstrate_verification_benefits()
    
    print("="*70)
    print("🎉 LLM Subdomain Verification Feature Summary")
    print("="*70)
    print("✓ Added LLM-based subdomain verification")
    print("✓ Filters organizational vs public domains")
    print("✓ Provides classification confidence and reasoning")
    print("✓ Reduces false positives in OSINT intelligence")
    print("✓ Maintains detailed logging and statistics")
    print("✓ Graceful fallback if LLM verification fails")
    print("\nThe workflow now provides high-quality, verified organizational subdomains!")

if __name__ == "__main__":
    asyncio.run(main())
