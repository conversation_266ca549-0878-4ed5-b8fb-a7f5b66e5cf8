#!/usr/bin/env python3
"""
Test script for Wikipedia LLM verification functionality
"""

import asyncio
import logging
from enhanced_workflow import EnhancedWikipediaSearchNode, EnhancedLLMNode

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_wikipedia_verification():
    """Test the new Wikipedia LLM verification feature with strict both-criteria filtering"""
    print("=== Wikipedia Strict Both-Criteria Pre-Filtering Test ===\n")

    # Create Wikipedia search node
    wiki_node = EnhancedWikipediaSearchNode()

    # Create and set verification node
    verification_node = EnhancedLLMNode("wikipedia_verification", "Wikipedia Relevance Verification", "wikipedia_relevance_verification")
    wiki_node.set_verification_node(verification_node)

    # Test case: Cybersecurity companies in Tokyo
    print("🔍 Testing: 'cybersecurity companies' in 'Tokyo'")
    print("   📋 STRICT RULE: Only articles relevant to BOTH location AND field are included")
    print("   ⚡ LLM verification applied BEFORE fetching page details for efficiency")
    result = await wiki_node.execute({
        "interest": "cybersecurity companies",
        "location": "Tokyo"
    })

    print(f"📊 Found {len(result)} strictly filtered results:\n")

    for i, res in enumerate(result, 1):
        print(f"{i}. 📄 {res['title']}")
        print(f"   🔗 {res['url']}")
        print(f"   🏷️  Type: {res['type']} (from query: '{res['query']}')")
        print(f"   📝 Summary: {res['text'][:100]}...")
        print(f"   ✅ Verified relevant to BOTH Tokyo AND cybersecurity companies")
        print()

async def test_verification_filtering():
    """Test that strict both-criteria verification properly filters results before fetching details"""
    print("\n=== Strict Both-Criteria Pre-Filtering Efficiency Test ===\n")

    # Create Wikipedia search node
    wiki_node = EnhancedWikipediaSearchNode()

    # Test without verification (should fetch all page details)
    print("🔍 Testing without verification (fetches all page details):")
    result_no_verification = await wiki_node.execute({
        "interest": "technology",
        "location": "Japan"
    })
    print(f"Results without verification: {len(result_no_verification)}")

    # Test with strict verification (should filter titles before fetching details)
    verification_node = EnhancedLLMNode("wikipedia_verification", "Wikipedia Relevance Verification", "wikipedia_relevance_verification")
    wiki_node.set_verification_node(verification_node)

    print("\n🔍 Testing with STRICT both-criteria verification:")
    print("   📋 Rule: Must be relevant to BOTH 'technology' AND 'Japan'")
    result_with_verification = await wiki_node.execute({
        "interest": "technology",
        "location": "Japan"
    })
    print(f"Results with strict verification: {len(result_with_verification)}")

    print(f"\nStrict filtering effect: {len(result_no_verification)} → {len(result_with_verification)} results")
    print("✅ Only articles relevant to BOTH location AND field are included!")
    print("⚡ LLM verification happens BEFORE fetching page details, saving time and resources!")

async def main():
    """Run all tests"""
    try:
        await test_wikipedia_verification()
        await test_verification_filtering()
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
