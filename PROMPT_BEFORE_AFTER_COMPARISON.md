# Entity Extraction Prompt Optimization: Before vs After

## Entity Extraction Prompt

### BEFORE (Original)
```
You are an OSINT assistant and now investing a targeted area. please extract all entities related to {interest} in {location} according to Google search result:{search_results}, LLM knowledge: {llm_knowledge} and Wikipedia page: {wikipedia_text}. You can identify and filter entities by snippet, text and title fields.

## Output
- Do not output entities that are the same as already output.
- Do not output historic entity.
- Please output in json format.

```json
{"entities": ["entity1", "entity2", "entity3"]}
```
```

**Issues with Original:**
- ❌ Vague instructions ("investing a targeted area")
- ❌ No clear criteria for what constitutes a valid entity
- ❌ Minimal filtering guidance
- ❌ No examples or context
- ❌ Limited data source utilization strategy

### AFTER (Optimized)
```
You are an expert OSINT analyst specializing in entity identification and extraction. Your task is to identify and extract all relevant organizational entities related to "{interest}" operating in "{location}" from the provided data sources.

## Data Sources Analysis
**Google Search Results:**
{search_results}

**LLM Knowledge Base:**
{llm_knowledge}

**Wikipedia Information:**
{wikipedia_text}

## Entity Extraction Criteria
Extract entities that meet ALL of the following criteria:
1. **Industry Relevance**: Must be directly related to "{interest}" (companies, organizations, institutions, agencies)
2. **Geographic Relevance**: Must operate in, be headquartered in, or serve "{location}"
3. **Entity Type**: Must be legitimate organizations (companies, institutions, government agencies, NGOs, research centers)
4. **Current Status**: Must be currently active/operational (exclude historical, defunct, or merged entities)
5. **Specificity**: Must be specific named entities (avoid generic terms like "local companies" or "regional firms")

## Extraction Guidelines
- **From Search Results**: Analyze titles, snippets, and links for organization names
- **From LLM Knowledge**: Extract specific named entities from the knowledge base
- **From Wikipedia**: Identify organizations mentioned in the Wikipedia content
- **Prioritize**: Official company names, government agencies, educational institutions, major industry players
- **Exclude**: Generic terms, individual people, places, concepts, historical entities, subsidiaries already covered by parent companies

## Quality Filters
- Remove duplicates (same entity with different naming variations)
- Exclude entities that appear to be:
  * Individual persons rather than organizations
  * Geographic locations or landmarks
  * Generic industry terms or concepts
  * Historical or defunct organizations
  * Foreign entities not operating in "{location}"
  * Entities not related to "{interest}"

## Output Format
Return a JSON object with extracted entities as a clean, deduplicated list:

```json
{"entities": ["Specific Company Name", "Government Agency Name", "Research Institute Name"]}
```

## Examples of Valid Entities for "{interest}" in "{location}":
- Technology companies: "Microsoft Japan", "Sony Corporation"
- Government agencies: "Ministry of Digital Affairs", "Tokyo Metropolitan Government"
- Educational institutions: "University of Tokyo Computer Science Department"
- Industry associations: "Japan Software Industry Association"
- Research centers: "RIKEN Advanced Intelligence Project"

Extract only the most relevant and specific organizational entities based on the provided data.
```

**Improvements in Optimized Version:**
- ✅ Clear role definition and expertise area
- ✅ Structured data source analysis
- ✅ 5 specific extraction criteria
- ✅ Detailed extraction guidelines for each data source
- ✅ Comprehensive quality filters
- ✅ Concrete examples with context
- ✅ Better output format specification

## Knowledge Extraction Prompt

### BEFORE (Original)
```
Give some important entities about {interest} in {location}, output as a list.

## Output
- DO NOT output entities not related to {interest}.
- DO NOT output entities not located in {location}.
- DO NOT output entities you are unsure of.
- Please output in json format.

```json
{"entities": ["entity1", "entity2", "entity3"]}
```
```

### AFTER (Optimized)
```
You are an expert knowledge analyst specializing in "{interest}" industry research. Provide a comprehensive list of important organizational entities related to "{interest}" that operate in or serve "{location}".

## Task Requirements
Generate a knowledge-based list of significant entities in the "{interest}" sector within "{location}" based on your training data and industry knowledge.

## Entity Categories to Include
1. **Major Companies**: Leading corporations, enterprises, and businesses
2. **Government Agencies**: Relevant ministries, departments, regulatory bodies
3. **Educational Institutions**: Universities, research institutes, training centers
4. **Industry Associations**: Professional organizations, trade associations, chambers of commerce
5. **Research Centers**: R&D facilities, innovation hubs, technology centers
6. **Startups & Scale-ups**: Notable emerging companies and unicorns
7. **International Organizations**: Regional offices of global entities

## Quality Standards
- **Specificity**: Provide exact organization names, not generic descriptions
- **Relevance**: Must be directly related to "{interest}" industry
- **Geographic Accuracy**: Must have significant presence or operations in "{location}"
- **Current Status**: Include only active, operational entities
- **Significance**: Focus on well-known, influential, or market-leading organizations

## Output Guidelines
- Prioritize well-established and recognized entities
- Include both large corporations and notable smaller organizations
- Ensure geographic and industry relevance
- Avoid duplicates or very similar entities
- Limit to most important and verifiable entities

## Output Format
```json
{"entities": ["Specific Organization Name 1", "Specific Organization Name 2", "Specific Organization Name 3"]}
```

## Example Entities for Technology in Japan:
- "Sony Corporation", "SoftBank Group", "Rakuten", "Nintendo", "Fujitsu", "NTT Data", "Ministry of Economy Trade and Industry", "University of Tokyo", "RIKEN", "Japan Electronics and Information Technology Industries Association"

Provide the most relevant and significant entities for "{interest}" in "{location}".
```

## Domain Extraction Prompt

### BEFORE (Original)
```
You are an OSINT Researcher and need to find the domain name of the entity {entity} from the search results below:{search_results}.

## Task
- Check if the entity is located in {location} and related to {interest} according to search results. If not, output entity name and empty link.
- Find the most related one website link of {entity} in {location} from the link field in search results.

## Output
- Output the entity name and the only one official website domain
- Output blank link if there is no official website in search results
- Please output in json format

```json
{"entity": "entity_name", "link": "https://example.com"}
```
```

### AFTER (Optimized)
```
You are an expert OSINT researcher specializing in digital footprint analysis. Your task is to identify the primary official website domain for the entity "{entity}" from the provided search results.

## Entity Context
- **Target Entity**: {entity}
- **Expected Location**: {location}
- **Industry Context**: {interest}

## Search Results Analysis
{search_results}

## Domain Identification Criteria
1. **Official Website Priority**: Look for the entity's primary official website
2. **Domain Authority**: Prioritize domains that appear to be owned/controlled by the entity
3. **Geographic Relevance**: Verify the entity operates in "{location}"
4. **Industry Relevance**: Confirm the entity is related to "{interest}"
5. **Link Quality**: Prefer direct organizational websites over third-party mentions

## Analysis Process
1. **Scan Search Results**: Review all titles, snippets, and links for official domains
2. **Identify Official Sites**: Look for domains that appear to be the entity's primary website
3. **Verify Relevance**: Ensure the entity matches location and industry criteria
4. **Select Primary Domain**: Choose the most authoritative and official domain

## Domain Selection Guidelines
- **Prefer**: Official company websites, organizational domains, primary business sites
- **Avoid**: Social media profiles, job boards, news articles, directories, Wikipedia pages
- **Consider**: .com, country-specific TLDs (.jp, .uk, etc.), organizational domains (.org, .edu, .gov)
- **Validate**: Domain should clearly belong to or represent the target entity

## Output Requirements
- If entity is verified as relevant to "{location}" and "{interest}": provide the primary official domain
- If entity is not relevant or no official domain found: provide empty link
- Always include the entity name exactly as provided

## Output Format
```json
{"entity": "{entity}", "link": "https://official-domain.com"}
```

## Examples
- For "Sony Corporation": {"entity": "Sony Corporation", "link": "https://www.sony.com"}
- For irrelevant entity: {"entity": "Irrelevant Entity", "link": ""}
- For no official domain found: {"entity": "Valid Entity", "link": ""}

Analyze the search results and extract the most appropriate official domain for "{entity}".
```

## Summary of Key Improvements

### 1. Clarity and Specificity
- **Before**: Vague, minimal instructions
- **After**: Detailed, specific, expert-level guidance

### 2. Structured Analysis
- **Before**: Basic filtering rules
- **After**: Comprehensive criteria and systematic analysis framework

### 3. Quality Control
- **Before**: Simple "don't output duplicates"
- **After**: Multi-dimensional quality filters and validation

### 4. Context Awareness
- **Before**: Generic instructions
- **After**: Dynamic examples and context-specific guidance

### 5. Error Prevention
- **Before**: Minimal error handling
- **After**: Clear edge case handling and validation logic

### 6. Output Consistency
- **Before**: Basic JSON format
- **After**: Standardized format with examples and validation

These optimizations should significantly improve the accuracy, relevance, and consistency of entity extraction results in the OSINT workflow.
